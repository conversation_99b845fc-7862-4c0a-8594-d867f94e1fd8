@extends('layouts.app')

@section('content')
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1 text-primary">Students in {{ $section->name }}</h2>
                <p class="text-muted mb-0 small">{{ $section->grade_level }}</p>
            </div>
            <a href="{{ route('admin.sections.index') }}" class="btn btn-secondary btn-action">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Sections
            </a>
        </div>

        <!-- Students Table -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary">Student ID</th>
                                <th class="text-primary">Name</th>
                                <th class="text-primary">Email</th>
                                <th class="text-primary">Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($students as $student)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <span class="fw-medium">{{ $student->student_id }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person-fill text-info"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $student->user->last_name }}, {{ $student->user->first_name }}</div>
                                                @if($student->user->middle_name)
                                                    <div class="small text-muted">{{ $student->user->middle_name }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ $student->user->email }}</span>
                                    </td>
                                    <td>
                                        @php
                                            // Get the most recent grade with remarks
                                            $latestGradeWithRemarks = $student->grades
                                                ->whereNotNull('remarks')
                                                ->where('remarks', '!=', '')
                                                ->sortByDesc('grading_period')
                                                ->first();

                                            // If no specific remarks, calculate overall performance
                                            if (!$latestGradeWithRemarks) {
                                                $latestQuarter = $student->grades->sortByDesc('grading_period')->first();
                                                if ($latestQuarter) {
                                                    $quarterGrades = $student->grades
                                                        ->where('grading_period', $latestQuarter->grading_period)
                                                        ->pluck('grade')
                                                        ->filter();

                                                    $average = $quarterGrades->isNotEmpty() ? $quarterGrades->avg() : null;
                                                    $remarks = $average !== null ? ($average >= 75 ? 'Passed' : 'Failed') : 'Not Graded';
                                                    $badgeClass = $average !== null ? ($average >= 75 ? 'success' : 'danger') : 'secondary';
                                                } else {
                                                    $remarks = 'Not Graded';
                                                    $badgeClass = 'secondary';
                                                }
                                            } else {
                                                $remarks = $latestGradeWithRemarks->remarks;
                                                $badgeClass = 'info';
                                            }
                                        @endphp

                                        <span class="badge bg-{{ $badgeClass }}">
                                            {{ $remarks }}
                                        </span>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-people fs-2 d-block mb-2"></i>
                                            No students found in this section
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Button Styling to Match Admin Students/Teachers */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            box-sizing: border-box;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* Badge Styling for Remarks */
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-sm i {
            font-size: 1rem;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .btn-action {
                padding: 10px 16px;
                font-size: 13px;
                min-width: auto;
            }

            .badge {
                font-size: 0.7rem;
                padding: 0.4rem 0.6rem;
            }
        }
    </style>
@endsection 