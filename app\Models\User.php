<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'last_name',
        'first_name',
        'middle_name',
        'suffix',
        'email',
        'password',
        'role',
        'section',
        'username',
        'profile_picture'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    public function student()
    {
        return $this->hasOne(Student::class);
    }

    /**
     * Check if the user is an active student
     *
     * @return bool
     */
    public function isActiveStudent(): bool
    {
        if ($this->role !== 'student') {
            return true; // Non-students are always considered "active" for this check
        }

        return $this->student && $this->student->status === 'active';
    }

    public function teacher()
    {
        return $this->hasOne(Teacher::class)->withTrashed();
    }

    public function subjects()
    {
        return $this->hasMany(Subject::class, 'teacher_id');
    }

    public function getFullNameAttribute()
    {
        $name = $this->first_name;
        if ($this->middle_name) {
            $name .= ' ' . $this->middle_name;
        }
        $name .= ' ' . $this->last_name;
        if ($this->suffix) {
            $name .= ' ' . $this->suffix;
        }
        return $name;
    }

    public function getFormalNameAttribute()
    {
        $name = $this->last_name;
        if ($this->suffix) {
            $name .= ' ' . $this->suffix;
        }
        $name .= ', ' . $this->first_name;
        if ($this->middle_name) {
            $name .= ' ' . $this->middle_name;
        }
        return $name;
    }

    public function getProfilePictureUrlAttribute()
    {
        if ($this->hasProfilePicture()) {
            return route('profile.picture', ['filename' => basename($this->profile_picture)]);
        }
        return asset('images/default-avatar.png');
    }

    public function hasProfilePicture()
    {
        return !empty($this->profile_picture) && file_exists(storage_path('app/private/' . $this->profile_picture));
    }

    public function getProfilePictureOrDefaultAttribute()
    {
        if ($this->hasProfilePicture()) {
            return route('profile.picture', ['filename' => basename($this->profile_picture)]);
        }
        return asset('images/default-avatar.png');
    }
}
