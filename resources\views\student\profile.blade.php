@extends('layouts.studentApp')

@section('content')
<div class="container-fluid py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">My Profile</h2>
            <p class="text-muted mb-0 small">Manage your account settings</p>
        </div>
        <a href="{{ route('student.gradebook') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i> Back
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <!-- Profile Summary -->
    <div class="profile-summary-compact">
        <div class="profile-summary-content">
            <div class="profile-avatar-compact">
                @if($user->hasProfilePicture())
                    <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="profile-img-compact">
                @else
                    <div class="profile-default-compact">
                        {{ strtoupper(substr($user->first_name, 0, 1)) }}{{ strtoupper(substr($user->last_name, 0, 1)) }}
                    </div>
                @endif
                <div class="status-compact">
                    <span class="status-dot"></span>
                </div>
            </div>
            <div class="profile-info-compact">
                <h4 class="profile-name-compact">{{ $user->full_name }}</h4>
                <div class="profile-details-compact">
                    <span class="profile-role-compact">
                        <i class="bi bi-shield-check me-1"></i>{{ ucfirst($user->role) }} Account
                    </span>
                    <span class="profile-divider">•</span>
                    <span class="profile-email-compact">
                        <i class="bi bi-envelope me-1"></i>{{ $user->email }}
                    </span>
                    <span class="profile-divider">•</span>
                    <span class="profile-username-compact">
                        <i class="bi bi-person-badge me-1"></i>{{ $user->username ?? 'Not Set' }}
                    </span>
                </div>
            </div>
            <div class="profile-status-compact">
                <span class="status-badge-compact">
                    <i class="bi bi-circle-fill me-1"></i>Active
                </span>
            </div>
        </div>
    </div>

    <!-- Enhanced Profile Form -->
    <div class="profile-form-container">
        <div class="form-header">
            <h3 class="form-title">
                <i class="bi bi-person-gear me-2"></i>Profile Settings
            </h3>
            <p class="form-subtitle">Update your profile picture and password</p>
        </div>

        <form action="{{ route('student.profile.update') }}" method="POST" enctype="multipart/form-data" class="profile-form">
            @csrf
            @method('PUT')

            <!-- Profile Picture Section -->
            <div class="form-section">
                <div class="section-header">
                    <h5 class="section-title">
                        <i class="bi bi-camera me-2"></i>Profile Picture
                    </h5>
                    <p class="section-subtitle">Upload a professional photo for your profile</p>
                </div>

                <div class="picture-upload-area">
                    <div class="picture-preview-container">
                        <div id="profilePicturePreview" class="picture-preview">
                            @if($user->hasProfilePicture())
                                <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="preview-image">
                            @else
                                <div class="preview-placeholder">
                                    <i class="bi bi-person-workspace"></i>
                                    <span>No Image</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="picture-upload-controls">
                        <div class="upload-input-group">
                            <label class="upload-label">
                                <i class="bi bi-cloud-upload me-2"></i>Choose Profile Picture
                            </label>
                            <input type="file" name="profile_picture"
                                   class="upload-input @error('profile_picture') is-invalid @enderror"
                                   accept="image/*" id="profilePictureInput">
                            @error('profile_picture')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                            <div class="upload-hint">Maximum 2MB • JPEG, PNG, JPG, GIF</div>
                        </div>

                        <div id="profilePictureActions" class="picture-actions">
                            @if($user->hasProfilePicture())
                                <button type="button" class="btn-remove" id="removeProfilePicture">
                                    <i class="bi bi-trash me-1"></i> Remove Picture
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Information Section (Read-only) -->
            <div class="form-section">
                <div class="section-header">
                    <h5 class="section-title">
                        <i class="bi bi-person-lines-fill me-2"></i>Personal Information
                    </h5>
                    <p class="section-subtitle">Your personal details (managed by administrator)</p>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-person me-2"></i>Last Name
                        </label>
                        <input type="text" class="enhanced-input readonly-input"
                               value="{{ $user->last_name }}" readonly>
                    </div>

                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-person me-2"></i>First Name
                        </label>
                        <input type="text" class="enhanced-input readonly-input"
                               value="{{ $user->first_name }}" readonly>
                    </div>

                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-person me-2"></i>Middle Name
                        </label>
                        <input type="text" class="enhanced-input readonly-input"
                               value="{{ $user->middle_name ?? 'Not Set' }}" readonly>
                    </div>

                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-person me-2"></i>Suffix
                        </label>
                        <input type="text" class="enhanced-input readonly-input"
                               value="{{ $user->suffix ?? 'Not Set' }}" readonly>
                    </div>

                    <div class="form-group form-group-full">
                        <label class="enhanced-label">
                            <i class="bi bi-envelope me-2"></i>Email Address
                        </label>
                        <input type="email" class="enhanced-input readonly-input"
                               value="{{ $user->email }}" readonly>
                    </div>
                </div>

                <div class="readonly-notice">
                    <i class="bi bi-info-circle me-2"></i>
                    Personal information can only be updated by the administrator. Contact your admin if changes are needed.
                </div>
            </div>

            <!-- Password Section -->
            <div class="form-section">
                <div class="section-header">
                    <h5 class="section-title">
                        <i class="bi bi-shield-lock me-2"></i>Security Settings
                    </h5>
                    <p class="section-subtitle">Update your password to keep your account secure</p>
                </div>

                <div class="password-notice">
                    <i class="bi bi-info-circle me-2"></i>
                    Leave password fields empty if you don't want to change your current password
                </div>

                <div class="form-grid">
                    <div class="form-group form-group-full">
                        <label class="enhanced-label">
                            <i class="bi bi-key me-2"></i>Current Password
                        </label>
                        <input type="password" name="current_password"
                            class="enhanced-input @error('current_password') is-invalid @enderror"
                            placeholder="Enter your current password">
                        @error('current_password')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-key me-2"></i>New Password
                        </label>
                        <input type="password" name="new_password"
                            class="enhanced-input @error('new_password') is-invalid @enderror"
                            placeholder="Enter new password">
                        @error('new_password')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="enhanced-label">
                            <i class="bi bi-key me-2"></i>Confirm New Password
                        </label>
                        <input type="password" name="new_password_confirmation"
                            class="enhanced-input"
                            placeholder="Confirm new password">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn-save">
                    <i class="bi bi-check-circle me-2"></i>Save Changes
                </button>
                <button type="button" class="btn-cancel" onclick="window.location.reload()">
                    <i class="bi bi-x-circle me-2"></i>Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Remove Profile Picture Form -->
<form id="removeProfilePictureForm" action="{{ route('student.profile.picture.remove') }}" method="POST" style="display: none;">
    @csrf
    @method('DELETE')
</form>

<style>
    /* Compact Profile Summary Styles */
    .profile-summary-compact {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 16px;
        margin-bottom: 1.5rem;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        overflow: hidden;
        animation: slideInUp 0.6s ease-out;
    }

    .profile-summary-content {
        display: flex;
        align-items: center;
        padding: 1.25rem 1.5rem;
        gap: 1rem;
        position: relative;
    }

    .profile-summary-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }

    .profile-avatar-compact {
        position: relative;
        flex-shrink: 0;
        z-index: 2;
    }

    .profile-img-compact {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.3);
        object-fit: cover;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .profile-default-compact {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: 700;
        color: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .status-compact {
        position: absolute;
        bottom: 2px;
        right: 2px;
    }

    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #10b981;
        border: 2px solid white;
        display: block;
        box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);
    }

    .profile-info-compact {
        flex: 1;
        z-index: 2;
    }

    .profile-name-compact {
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0 0 0.25rem 0;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .profile-details-compact {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: rgba(255, 255, 255, 0.9);
    }

    .profile-role-compact,
    .profile-email-compact,
    .profile-username-compact {
        display: flex;
        align-items: center;
    }

    .profile-divider {
        color: rgba(255, 255, 255, 0.6);
        font-weight: bold;
    }

    .profile-status-compact {
        z-index: 2;
    }

    .status-badge-compact {
        background: rgba(16, 185, 129, 0.3);
        color: white;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Enhanced Form Styles */
    .profile-form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        animation: slideInUp 0.6s ease-out 0.2s both;
    }

    .form-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 2rem;
        border-bottom: 1px solid #e2e8f0;
    }

    .form-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
    }

    .form-subtitle {
        color: #64748b;
        margin: 0;
        font-size: 0.95rem;
    }

    .profile-form {
        padding: 0;
    }

    .form-section {
        padding: 2rem;
        border-bottom: 1px solid #f1f5f9;
    }

    .form-section:last-of-type {
        border-bottom: none;
    }

    .section-header {
        margin-bottom: 1.5rem;
    }

    .section-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.5rem 0;
        display: flex;
        align-items: center;
    }

    .section-subtitle {
        color: #64748b;
        margin: 0;
        font-size: 0.875rem;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group-full {
        grid-column: 1 / -1;
    }

    .enhanced-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
    }

    .enhanced-input {
        padding: 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #fafafa;
    }

    .enhanced-input:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .enhanced-input.is-invalid {
        border-color: #ef4444;
        background: #fef2f2;
    }

    .readonly-input {
        background: #f1f5f9 !important;
        color: #64748b;
        cursor: not-allowed;
    }

    .readonly-input:focus {
        border-color: #e5e7eb !important;
        box-shadow: none !important;
    }

    .readonly-notice {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1.5rem;
        color: #0369a1;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
    }

    .error-message {
        color: #ef4444;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        font-weight: 500;
    }

    /* Picture Upload Styles */
    .picture-upload-area {
        display: flex;
        gap: 2rem;
        align-items: flex-start;
    }

    .picture-preview-container {
        flex-shrink: 0;
    }

    .picture-preview {
        width: 120px;
        height: 120px;
        border-radius: 16px;
        overflow: hidden;
        border: 3px dashed #d1d5db;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9fafb;
        transition: all 0.3s ease;
    }

    .picture-preview:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .preview-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #9ca3af;
        font-size: 0.875rem;
    }

    .preview-placeholder i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .picture-upload-controls {
        flex: 1;
    }

    .upload-input-group {
        margin-bottom: 1rem;
    }

    .upload-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
    }

    .upload-input {
        padding: 0.875rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #fafafa;
        width: 100%;
    }

    .upload-input:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .upload-hint {
        color: #6b7280;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }

    .picture-actions {
        display: flex;
        gap: 0.75rem;
    }

    .btn-remove {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .btn-remove:hover {
        background: #fee2e2;
        border-color: #fca5a5;
    }

    /* Password Notice */
    .password-notice {
        background: #eff6ff;
        border: 1px solid #bfdbfe;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        color: #1e40af;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
    }

    /* Form Actions */
    .form-actions {
        padding: 2rem;
        background: #f8fafc;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .btn-save {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.875rem 2rem;
        border-radius: 12px;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .btn-cancel {
        background: #f3f4f6;
        color: #6b7280;
        border: 1px solid #d1d5db;
        padding: 0.875rem 2rem;
        border-radius: 12px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .btn-cancel:hover {
        background: #e5e7eb;
        color: #4b5563;
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
        .profile-summary-content {
            flex-direction: column;
            text-align: center;
            gap: 1rem;
            padding: 1rem;
        }

        .profile-details-compact {
            flex-direction: column;
            gap: 0.25rem;
        }

        .profile-divider {
            display: none;
        }

        .form-header {
            padding: 1.5rem;
        }

        .form-section {
            padding: 1.5rem;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .picture-upload-area {
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .form-actions {
            padding: 1.5rem;
            flex-direction: column;
        }

        .btn-save,
        .btn-cancel {
            width: 100%;
            justify-content: center;
        }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const profilePictureInput = document.getElementById('profilePictureInput');
        const profilePicturePreview = document.getElementById('profilePicturePreview');
        const profilePictureActions = document.getElementById('profilePictureActions');
        const removeProfilePictureBtn = document.getElementById('removeProfilePicture');

        let originalImageExists = {{ $user->hasProfilePicture() ? 'true' : 'false' }};
        let hasNewImage = false;

        // Handle file input change for preview
        if (profilePictureInput) {
            profilePictureInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Update preview
                        profilePicturePreview.innerHTML = `<img src="${e.target.result}" alt="Profile Picture Preview" class="preview-image">`;

                        // Update profile summary avatar
                        updateProfileSummaryAvatar(e.target.result);

                        // Update button to "Clear Image"
                        hasNewImage = true;
                        updateActionButton();
                    };
                    reader.readAsDataURL(file);
                } else {
                    // Reset to original state if no file selected
                    resetToOriginalState();
                }
            });
        }

        function updateActionButton() {
            if (hasNewImage) {
                // Show "Clear Image" button when new image is selected
                profilePictureActions.innerHTML = `
                    <button type="button" class="btn-remove" id="clearImageBtn">
                        <i class="bi bi-x-circle me-1"></i> Clear Image
                    </button>
                `;

                // Add event listener to clear button
                document.getElementById('clearImageBtn').addEventListener('click', function() {
                    clearSelectedImage();
                });
            } else if (originalImageExists) {
                // Show "Remove Picture" button for existing images
                profilePictureActions.innerHTML = `
                    <button type="button" class="btn-remove" id="removeProfilePicture">
                        <i class="bi bi-trash me-1"></i> Remove Picture
                    </button>
                `;

                // Add event listener to remove button
                document.getElementById('removeProfilePicture').addEventListener('click', function() {
                    if (confirm('Are you sure you want to remove your profile picture?')) {
                        document.getElementById('removeProfilePictureForm').submit();
                    }
                });
            } else {
                // No buttons needed if no image exists and no new image selected
                profilePictureActions.innerHTML = '';
            }
        }

        function clearSelectedImage() {
            // Clear the file input
            profilePictureInput.value = '';
            hasNewImage = false;

            // Reset to original state
            resetToOriginalState();
        }

        function resetToOriginalState() {
            hasNewImage = false;

            // Reset preview to original state
            if (originalImageExists) {
                profilePicturePreview.innerHTML = `
                    <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="preview-image">
                `;
                // Reset profile summary to original image
                updateProfileSummaryAvatar('{{ $user->profile_picture_url }}');
            } else {
                profilePicturePreview.innerHTML = `
                    <div class="preview-placeholder">
                        <i class="bi bi-person-workspace"></i>
                        <span>No Image</span>
                    </div>
                `;
                // Reset profile summary to default avatar
                updateProfileSummaryAvatar(null);
            }

            // Update button
            updateActionButton();
        }

        // Update profile summary avatar when image changes
        function updateProfileSummaryAvatar(imageSrc) {
            const summaryAvatar = document.querySelector('.profile-avatar-compact');
            if (summaryAvatar) {
                if (imageSrc) {
                    summaryAvatar.innerHTML = `
                        <img src="${imageSrc}" alt="Profile Picture" class="profile-img-compact">
                        <div class="status-compact">
                            <span class="status-dot"></span>
                        </div>
                    `;
                } else {
                    summaryAvatar.innerHTML = `
                        <div class="profile-default-compact">
                            {{ strtoupper(substr($user->first_name, 0, 1)) }}{{ strtoupper(substr($user->last_name, 0, 1)) }}
                        </div>
                        <div class="status-compact">
                            <span class="status-dot"></span>
                        </div>
                    `;
                }
            }
        }

        // Handle remove profile picture (for existing functionality)
        if (removeProfilePictureBtn) {
            removeProfilePictureBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to remove your profile picture?')) {
                    document.getElementById('removeProfilePictureForm').submit();
                }
            });
        }
    });
</script>
@endsection