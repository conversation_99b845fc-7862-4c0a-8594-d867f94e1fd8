<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Section;
use Illuminate\Support\Facades\DB;
use App\Models\EventView;
use League\Csv\Reader;
use League\Csv\Writer;
use Illuminate\Support\Facades\Storage;

class StudentController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'student')
            ->with(['student', 'student.section'])
            ->orderBy('last_name')
            ->orderBy('first_name');

        // Apply filters
        if ($request->filled('status')) {
            $query->whereHas('student', function ($q) use ($request) {
                $q->where('status', $request->status);
            });
        }

        if ($request->filled('grade_level')) {
            $query->whereHas('student', function ($q) use ($request) {
                $q->where('grade_level', $request->grade_level);
            });
        }

        if ($request->filled('section')) {
            $query->whereHas('student', function ($q) use ($request) {
                $q->where('section_id', $request->section);
            });
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                    ->orWhere('last_name', 'like', "%{$search}%")
                    ->orWhere('middle_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhereHas('student', function ($q) use ($search) {
                        $q->where('student_id', 'like', "%{$search}%");
                    });
            });
        }

        $students = $query->paginate(10)->withQueryString();

        // Get sections with student counts
        $sections = Section::withCount(['students' => function($query) {
            $query->whereHas('user', function($q) {
                $q->where('role', 'student');
            });
        }])
        ->orderBy('grade_level')
        ->orderBy('name')
        ->get();

        return view('admin.students.index', compact('students', 'sections'));
    }

    public function create()
    {
        return view('admin.students.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'last_name' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:users,email',
            'lrn' => 'required|string|unique:students,lrn',
        ]);

        // Get the latest student ID and generate the next one
        $latestStudent = \App\Models\Student::orderBy('student_id', 'desc')->first();
        $nextId = $latestStudent ? intval(substr($latestStudent->student_id, 3)) + 1 : 1;
        $studentId = 'STU' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

        // Generate temporary password based on student ID
        $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

        // Create user record
        $user = new \App\Models\User();
        $user->last_name = $request->last_name;
        $user->first_name = $request->first_name;
        $user->middle_name = $request->middle_name;
        $user->suffix = $request->suffix;
        $user->email = $request->email;
        $user->username = $studentId;
        $user->password = bcrypt($tempPassword);
        $user->role = 'student';
        $user->save();

        // Create student record
        $student = new \App\Models\Student();
        $student->user_id = $user->id;
        $student->student_id = $studentId;
        $student->lrn = $request->lrn;
        $student->street_address = $request->street_address;
        $student->barangay = $request->barangay;
        $student->municipality = $request->municipality;
        $student->province = $request->province;
        $student->phone = $request->phone;
        $student->birthdate = $request->birthdate;
        $student->gender = $request->gender;
        $student->status = 'active';
        $student->save();

        return redirect()->route('admin.students.index')
            ->with('success', 'Student added successfully! Temporary password: ' . $tempPassword);
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $user = User::findOrFail($id);

            // Delete related records that don't have foreign key constraints
            if ($user->student) {
                // Delete event views (if EventView model exists)
                if (class_exists('App\Models\EventView')) {
                    EventView::where('user_id', $user->id)->delete();
                }
            }

            // Delete the user record - this will cascade delete the student record
            // and the student deletion will cascade delete the grades
            $user->delete();

            DB::commit();
            return redirect()->route('admin.students.index')
                ->with('success', 'Student deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error deleting student: ' . $e->getMessage());
            return redirect()->route('admin.students.index')
                ->with('error', 'Failed to delete student. Please try again or contact administrator.');
        }
    }

    /**
     * Bulk delete multiple students
     */
    public function bulkDestroy(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'exists:users,id'
        ]);

        try {
            DB::beginTransaction();

            $studentIds = $request->student_ids;
            $deletedCount = 0;

            foreach ($studentIds as $id) {
                $user = User::find($id);

                if ($user && $user->role === 'student') {
                    // Delete related records that don't have foreign key constraints
                    if ($user->student) {
                        // Delete event views (if EventView model exists)
                        if (class_exists('App\Models\EventView')) {
                            EventView::where('user_id', $user->id)->delete();
                        }
                    }

                    // Delete the user record - this will cascade delete the student record
                    $user->delete();
                    $deletedCount++;
                }
            }

            DB::commit();

            $message = $deletedCount === 1
                ? 'Student deleted successfully!'
                : "{$deletedCount} students deleted successfully!";

            return redirect()->route('admin.students.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error bulk deleting students: ' . $e->getMessage());
            return redirect()->route('admin.students.index')
                ->with('error', 'Failed to delete selected students. Please try again or contact administrator.');
        }
    }

    public function edit($id)
    {
        $student = User::with('student')->findOrFail($id);
        return view('admin.students.edit', compact('student'));
    }

    public function update(Request $request, $id)
    {
        $user = User::with('student')->findOrFail($id);

        $request->validate([
            'last_name' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:users,email,' . $id,
            'password' => 'nullable|min:8|confirmed',
            'lrn' => 'required|string|unique:students,lrn,' . $user->student->user_id . ',user_id',
        ]);

        // Update user record
        $userData = [
            'last_name' => $request->last_name,
            'first_name' => $request->first_name,
            'middle_name' => $request->middle_name,
            'suffix' => $request->suffix,
            'email' => $request->email,
        ];

        // Only update password if provided
        if (!empty($request->password)) {
            $userData['password'] = bcrypt($request->password);
        }

        $user->update($userData);

        // Get the current status before updating
        $currentStatus = $user->student->status ?? 'active';
        $newStatus = $request->status ?? 'active';

        // Update student record
        $studentData = [
            'lrn' => $request->lrn,
            'street_address' => $request->street_address,
            'barangay' => $request->barangay,
            'municipality' => $request->municipality,
            'province' => $request->province,
            'phone' => $request->phone,
            'birthdate' => $request->birthdate,
            'gender' => $request->gender,
            'status' => $newStatus,
        ];

        \App\Models\Student::where('user_id', $user->id)->update($studentData);

        // Handle status change - if student status changed to inactive, log them out
        if ($currentStatus !== $newStatus && !$this->isStudentActive($newStatus)) {
            $this->logoutStudentSessions($user->id);
        }

        $statusMessage = '';
        if ($currentStatus !== $newStatus && !$this->isStudentActive($newStatus)) {
            $statusMessage = ' The student has been logged out and can no longer access the system.';
        }

        return redirect()->route('admin.students.index')
            ->with('success', 'Student updated successfully!' . $statusMessage);
    }

    /**
     * Check if student status allows system access
     *
     * @param string|null $status
     * @return bool
     */
    private function isStudentActive(?string $status): bool
    {
        // Only 'active' status allows system access
        // All other statuses (inactive, dropped, transferred, graduated) deny access
        return $status === 'active';
    }

    /**
     * Log out all sessions for a specific user
     *
     * @param int $userId
     * @return void
     */
    private function logoutStudentSessions(int $userId): void
    {
        // Delete all sessions for this user
        \DB::table('sessions')->where('user_id', $userId)->delete();

        // Log the action
        \Log::info("Student sessions terminated", [
            'user_id' => $userId,
            'reason' => 'Status changed to inactive',
            'timestamp' => now()
        ]);
    }

    public function show($id)
    {
        $student = User::with('student')->findOrFail($id);
        return view('admin.students.show', compact('student'));
    }

    /**
     * Download the CSV template for student import
     */
    public function downloadTemplate()
    {
        $csv = Writer::createFromString('');

        // Add headers
        $csv->insertOne([
            'last_name',
            'first_name',
            'middle_name',
            'suffix',
            'email',
            'lrn',
            'street_address',
            'barangay',
            'municipality',
            'province',
            'phone',
            'birthdate',
            'gender'
        ]);

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="student_import_template.csv"',
        ];

        return response($csv->toString(), 200, $headers);
    }

    /**
     * Import students from CSV file
     */
    public function importStudents(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240' // max 10MB
        ]);

        try {
            DB::beginTransaction();

            $csv = Reader::createFromPath($request->file('csv_file')->getPathname());
            $csv->setHeaderOffset(0);

            $records = $csv->getRecords();
            $importedCount = 0;
            $updatedCount = 0;
            $errors = [];

            foreach ($records as $index => $record) {
                try {
                    // Validate required fields
                    if (empty($record['last_name']) || empty($record['first_name']) || empty($record['lrn'])) {
                        throw new \Exception('Required fields missing');
                    }

                    // Clean and trim the LRN for consistent matching
                    $cleanLrn = trim($record['lrn']);

                    // Check if student exists with this LRN
                    $existingStudent = \App\Models\Student::where('lrn', $cleanLrn)->first();

                    \Log::info('CSV Import - LRN Check', [
                        'original_lrn' => $record['lrn'],
                        'clean_lrn' => $cleanLrn,
                        'existing_student_found' => $existingStudent ? true : false,
                        'existing_student_id' => $existingStudent?->user_id
                    ]);

                    if ($existingStudent) {
                        // Update existing student
                        $user = User::find($existingStudent->user_id);

                        // Check if email is being changed and if it already exists for another user
                        if (!empty($record['email']) && $record['email'] !== $user->email) {
                            if (User::where('email', $record['email'])->where('id', '!=', $user->id)->exists()) {
                                throw new \Exception('Email already exists for another user');
                            }
                        }

                        // Update user record
                        $user->last_name = $record['last_name'];
                        $user->first_name = $record['first_name'];
                        $user->middle_name = $record['middle_name'] ?? $user->middle_name;
                        $user->suffix = $record['suffix'] ?? $user->suffix;
                        $user->email = $record['email'] ?? $user->email;
                        $user->save();

                        // Convert date format from MM/DD/YYYY to YYYY-MM-DD if birthdate is provided
                        $birthdate = null;
                        if (!empty($record['birthdate'])) {
                            try {
                                $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                                if ($date) {
                                    $birthdate = $date->format('Y-m-d');
                                }
                            } catch (\Exception $e) {
                                // If date conversion fails, keep existing birthdate
                                $birthdate = $existingStudent->birthdate;
                            }
                        }

                        // Update student record using user_id
                        \App\Models\Student::where('user_id', $existingStudent->user_id)->update([
                            'street_address' => $record['street_address'] ?? $existingStudent->street_address,
                            'barangay' => $record['barangay'] ?? $existingStudent->barangay,
                            'municipality' => $record['municipality'] ?? $existingStudent->municipality,
                            'province' => $record['province'] ?? $existingStudent->province,
                            'phone' => $record['phone'] ?? $existingStudent->phone,
                            'birthdate' => $birthdate ?? $existingStudent->birthdate,
                            'gender' => $record['gender'] ?? $existingStudent->gender,
                            'grade_level' => $record['grade_level'] ?? $existingStudent->grade_level,
                            'section' => $record['section'] ?? $existingStudent->section
                        ]);

                        $updatedCount++;
                    } else {
                        // Create new student
                        // Check if email already exists (only if email is provided)
                        if (!empty($record['email']) && User::where('email', $record['email'])->exists()) {
                            throw new \Exception('Email already exists');
                        }

                        // Get the latest student ID and generate the next one
                        $latestStudent = \App\Models\Student::orderBy('student_id', 'desc')->first();
                        $nextId = $latestStudent ? intval(substr($latestStudent->student_id, 3)) + 1 : 1;
                        $studentId = 'STU' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

                        // Generate temporary password based on student ID
                        $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

                        // Create user record
                        $user = new User();
                        $user->last_name = $record['last_name'];
                        $user->first_name = $record['first_name'];
                        $user->middle_name = $record['middle_name'] ?? null;
                        $user->suffix = $record['suffix'] ?? null;
                        $user->email = $record['email'] ?? null;
                        $user->username = $studentId;
                        $user->password = bcrypt($tempPassword);
                        $user->role = 'student';
                        $user->save();

                        // Create student record
                        $student = new \App\Models\Student();
                        $student->user_id = $user->id;
                        $student->student_id = $studentId;
                        $student->lrn = $cleanLrn;
                        $student->street_address = $record['street_address'] ?? null;
                        $student->barangay = $record['barangay'] ?? null;
                        $student->municipality = $record['municipality'] ?? null;
                        $student->province = $record['province'] ?? null;
                        $student->phone = $record['phone'] ?? null;
                        $student->birthdate = $record['birthdate'] ?? null;
                        $student->gender = $record['gender'] ?? null;
                        $student->grade_level = $record['grade_level'] ?? null;
                        $student->section = $record['section'] ?? null;
                        $student->status = 'active';
                        $student->save();

                        $importedCount++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            DB::commit();

            $message = [];
            if ($importedCount > 0) {
                $message[] = "Imported {$importedCount} new students";
            }
            if ($updatedCount > 0) {
                $message[] = "Updated {$updatedCount} existing students";
            }

            $successMessage = implode('. ', $message);

            if (count($errors) > 0) {
                $errorMessage = "Encountered " . count($errors) . " errors during import:\n" . implode("\n", $errors);
                return redirect()->route('admin.students.index')
                    ->with('success', $successMessage)
                    ->with('error', $errorMessage);
            }

            return redirect()->route('admin.students.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.students.index')
                ->with('error', 'Failed to import students. Please check your CSV file format and try again.');
        }
    }

    /**
     * Bulk assign grade level and section to multiple students
     */
    public function bulkAssign(Request $request)
    {
        $request->validate([
            'student_ids' => 'required|json',
            'grade_level' => 'required|string',
            'section_id' => 'required|exists:sections,id',
        ]);

        try {
            DB::beginTransaction();

            $studentIds = json_decode($request->student_ids, true);

            if (!is_array($studentIds) || empty($studentIds)) {
                throw new \Exception('Invalid student selection');
            }

            // Get the section details
            $section = \App\Models\Section::findOrFail($request->section_id);

            $updatedCount = 0;

            foreach ($studentIds as $userId) {
                $user = User::find($userId);

                if ($user && $user->role === 'student' && $user->student) {
                    \App\Models\Student::where('user_id', $user->id)->update([
                        'grade_level' => $request->grade_level,
                        'section' => $section->section_id, // Use section_id instead of id
                    ]);
                    $updatedCount++;
                }
            }

            DB::commit();

            $message = $updatedCount === 1
                ? "Grade level and section assigned to 1 student successfully! (Section: {$section->name})"
                : "Grade level and section assigned to {$updatedCount} students successfully! (Section: {$section->name})";

            return redirect()->route('admin.students.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error bulk assigning students: ' . $e->getMessage());
            return redirect()->route('admin.students.index')
                ->with('error', 'Failed to assign grade level and section. Please try again.');
        }
    }
}

