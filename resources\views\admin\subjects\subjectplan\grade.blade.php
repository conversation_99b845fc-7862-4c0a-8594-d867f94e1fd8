@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">{{ $grade }} Subject Labels</h2>
            <p class="text-muted mb-0 small">Manage subject labels for {{ $grade }} level</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.subjects.plan') }}" class="btn btn-secondary btn-action">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Academic Program
            </a>
            <button type="button" class="btn btn-primary btn-action" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                <i class="bi bi-plus-circle me-2"></i>
                Add Subject Label
            </button>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Main Content -->
    <div class="card shadow-sm border-0">
        <div class="card-body p-4">
            <div class="row g-4">
                @forelse($subjects as $subject)
                    <div class="col-md-4 col-lg-3">
                        <div class="card h-100 border-0 shadow-sm hover-card">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <h5 class="card-title mb-0">{{ $subject->name }}</h5>
                                    <div class="dropdown">
                                        <button class="btn btn-link text-muted p-0" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li>
                                                <button type="button" class="dropdown-item" 
                                                        data-bs-toggle="modal" 
                                                        data-bs-target="#editSubjectModal"
                                                        data-subject-id="{{ $subject->id }}"
                                                        data-subject-name="{{ $subject->name }}">
                                                    <i class="bi bi-pencil me-2"></i> Edit
                                                </button>
                                            </li>
                                            <li>
                                                <form action="{{ route('admin.subjects.destroy', $subject->id) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="dropdown-item text-danger" 
                                                            onclick="return confirm('Are you sure you want to remove this subject label?')">
                                                        <i class="bi bi-trash me-2"></i> Remove
                                                    </button>
                                                </form>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-12">
                        <div class="text-center py-5">
                            <div class="text-muted">
                                <i class="bi bi-inbox-fill fs-1 d-block mb-3"></i>
                                No subject labels assigned to this level
                            </div>
                            <button type="button" class="btn btn-primary btn-action mt-3" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
                                <i class="bi bi-plus-circle me-2"></i>
                                Add Subject Label
                            </button>
                        </div>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>

<!-- Add Subject Modal -->
<div class="modal fade" id="addSubjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Subject Label</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.subjects.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="grade_level" value="{{ $grade }}">
                    <input type="hidden" name="status" value="active">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Subject Label Name</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" 
                               placeholder="Enter subject label name" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-action" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-action">
                        <i class="bi bi-check-circle me-2"></i>
                        Add Subject Label
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Subject Modal -->
<div class="modal fade" id="editSubjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Subject Label</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSubjectForm" action="" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Subject Label Name</label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="edit_name" name="name" 
                               placeholder="Enter subject label name" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary btn-action" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary btn-action">
                        <i class="bi bi-check-circle me-2"></i>
                        Update Subject Label
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* CSS Variables */
:root {
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Button Styling to Match Create Page */
.btn-action {
    display: inline-flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.btn-primary.btn-action {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary.btn-action:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    transform: translateY(-2px);
    color: white;
}

.btn-secondary.btn-action {
    background: #ffffff;
    color: #64748b;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary.btn-action:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #374151;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Enhanced Card Styling */
.hover-card {
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.hover-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
    border-color: rgba(99, 102, 241, 0.2);
}

.card {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Alert Styling */
.alert {
    border: none;
    border-radius: 12px;
    border-left: 4px solid;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #ecfdf5);
    border-left-color: #10b981;
    color: #065f46;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fef2f2);
    border-left-color: #ef4444;
    color: #991b1b;
}

/* Enhanced Dropdown Styling */
.dropdown-menu {
    border: none;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-radius: 12px;
    padding: 8px;
    backdrop-filter: blur(8px);
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
    padding: 8px 12px;
    border-radius: 8px;
    transition: var(--transition);
    font-weight: 500;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
    color: var(--primary-dark);
}

.dropdown-item.text-danger:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(254, 226, 226, 0.5));
    color: #dc2626;
}

.dropdown-item i {
    font-size: 0.875rem;
    width: 16px;
    text-align: center;
}

/* Enhanced Button Link */
.btn-link {
    text-decoration: none;
    transition: var(--transition);
    border-radius: 8px;
    padding: 8px;
}

.btn-link:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: scale(1.1);
}

/* Enhanced Modal Styling */
.modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(8px);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 20px 24px;
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    border-radius: 16px 16px 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    padding: 20px 24px;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-radius: 0 0 16px 16px;
}

.modal-body {
    padding: 24px;
}

.modal-title {
    font-weight: 600;
    color: #1e293b;
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    transition: var(--transition);
    padding: 12px 16px;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .btn-action {
        padding: 10px 16px;
        font-size: 13px;
        min-width: auto;
    }

    .card {
        border-radius: 12px;
    }

    .modal-content {
        border-radius: 12px;
    }
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit modal
    const editModal = document.getElementById('editSubjectModal');
    if (editModal) {
        editModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const subjectId = button.getAttribute('data-subject-id');
            const subjectName = button.getAttribute('data-subject-name');
            
            const form = this.querySelector('#editSubjectForm');
            const nameInput = this.querySelector('#edit_name');
            
            // Update form action with the correct route
            form.action = "{{ url('admin/subjects') }}/" + subjectId;
            nameInput.value = subjectName;
        });
    }
});
</script>
@endpush
@endsection 