<?php

namespace App\Http\Controllers\Teacher;

use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Student;
use App\Models\User;
use App\Models\Subject;
use App\Models\Event;
use App\Models\Grade;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use App\Models\Section;
use Illuminate\Support\Facades\DB;
use App\Models\EventView;
use League\Csv\Reader;
use League\Csv\Writer;

use App\Services\AutomaticEnrollmentService;

class TeacherController extends Controller
{
    protected $enrollmentService;

    public function __construct(AutomaticEnrollmentService $enrollmentService)
    {
        $this->enrollmentService = $enrollmentService;
    }

    /**
     * Show the teacher dashboard.
     */
    public function dashboard()
    {
        $teacher = auth()->user();
        
        // Get the teacher's assigned section
        $section = Section::where('adviser_id', $teacher->id)->first();
        
        // Get students count and list from the teacher's section
        $students = collect();
        $studentsCount = 0;
        if ($section) {
            $studentsCount = Student::where('section', $section->name)
                ->where('status', 'active')
                ->whereNull('deleted_at')
                ->count();
        }
        
        // Get subjects count and list assigned to this teacher
        $subjects = Subject::where('teacher_id', $teacher->id)->get();
        $subjectsCount = $subjects->count();
        
        // Get pending grades count
        $pendingGrades = Grade::whereIn('subject_id', $subjects->pluck('id'))
            ->whereNull('grade')
            ->count();
        
        // Get upcoming events that haven't been viewed by this teacher
        $events = Event::where(function($query) {
                $query->where('visibility', 'All')
                      ->orWhere('visibility', 'Teachers');
            })
            ->where('event_date', '>=', now())
            ->whereNotExists(function($query) use ($teacher) {
                $query->select(DB::raw(1))
                    ->from('event_views')
                    ->whereColumn('event_views.event_id', 'events.event_id')
                    ->where('event_views.user_id', $teacher->id);
            })
            ->orderBy('event_date', 'asc')
            ->take(5)
            ->get();
        $upcomingEvents = $events->count();
        
        // Get recent activities (last 5 grade submissions)
        $activities = Grade::with(['student.user', 'subject'])
            ->whereIn('subject_id', $subjects->pluck('id'))
            ->whereNotNull('grade')
            ->orderBy('updated_at', 'desc')
            ->take(5)
            ->get()
            ->map(function($grade) {
                return "Graded {$grade->student->user->name} in {$grade->subject->name}";
            });

        return view('teachers.dashboard', compact(
            'students',
            'studentsCount',
            'subjects',
            'subjectsCount',
            'pendingGrades',
            'events',
            'upcomingEvents',
            'activities'
        ));
    }

    /**
     * Show the create student form.
     */
    public function createStudent()
    {
        $teacher = auth()->user();
        $section = Section::where('adviser_id', $teacher->id)->first();
        
        if (!$section) {
            return redirect()->route('teachers.student.index')
                ->with('error', 'You are not assigned to any section. Please contact the administrator.');
        }

        return view('teachers.student.create', compact('section'));
    }

    /**
     * Show the student index page.
     */
    public function student()
    {
        $students = User::where('role', 'student')
            ->join('students', 'users.id', '=', 'students.user_id')
            ->select('users.*', 'students.*')
            ->orderBy('users.last_name')
            ->orderBy('users.first_name')
            ->get();
        return view('teachers.student.index', compact('students'));
    }

    /**
     * Show the grades index page.
     */
    public function gradeIndex()
    {
        return view('teachers.grade.index');
    }

    /**
     * Show the subjects index page.
     */
    public function subjectIndex()
    {
        $teacher = auth()->user();
        
        // Get the teacher's assigned section
        $section = Section::where('adviser_id', $teacher->id)->first();
        
        // Get subjects assigned to this teacher
        $subjects = Subject::with(['schedules', 'grades.user'])
            ->where('teacher_id', Auth::id())
            ->get();

        return view('teachers.subject.index', compact('subjects', 'section'));
    }

    /**
     * Show the list of students.
     */
    public function indexStudent()
    {
        $teacher = auth()->user();
        
        // Get the section where the teacher is assigned as adviser
        $section = Section::where('adviser_id', $teacher->id)->first();
        
        if (!$section) {
            return view('teachers.student.index', [
                'students' => collect(),
                'section' => null,
                'error' => 'You are not assigned to any section. Please contact the administrator.'
            ]);
        }

        // Get active students from the teacher's assigned section with user relationship
        $students = Student::with('user')
            ->join('users', 'students.user_id', '=', 'users.id')
            ->where('students.section', $section->name)
            ->where('students.status', 'active')
            ->whereNull('students.deleted_at')
            ->orderBy('users.last_name')
            ->orderBy('users.first_name')
            ->select('students.*')
            ->paginate(10);

        return view('teachers.student.index', compact('students', 'section'));
    }

    public function indexStudentGrade()
    {
        $teacher = auth()->user();
        
        // Get the section where the teacher is assigned as adviser
        $section = Section::where('adviser_id', $teacher->id)->first();
        
        if (!$section) {
            return view('teachers.student.grade.index', [
                'students' => collect(),
                'subjects' => collect(),
                'grades' => collect(),
                'error' => 'You are not assigned to any section. Please contact the administrator.'
            ]);
        }

        // Get students from the teacher's assigned section with their grades
        $students = User::where('role', 'student')
            ->join('students', 'users.id', '=', 'students.user_id')
            ->where('students.section', $section->name)
            ->select('users.*', 'students.*')
            ->with(['student.grades' => function($query) {
                $query->orderBy('grading_period', 'desc');
            }])
            ->orderBy('users.last_name')
            ->orderBy('users.first_name')
            ->paginate(10);

        return view('teachers.student.grade.index', compact('students'));
    }

    public function showStudentGrade($id)
    {
        $teacher = auth()->user();
        
        // Get the student with their user information
        $student = Student::with(['user', 'automaticEnrollments'])
            ->where('user_id', $id)
            ->firstOrFail();
            
        // Get all subjects for the student's grade level from the admin side
        $subjects = Subject::where('grade_level', $student->grade_level)
            ->whereNull('parent_id')  // Only get parent subjects (subject labels)
            ->where('status', 'active')  // Only get active subjects
            ->orderBy('name')
            ->get();
            
        // Get all grades for this student
        $grades = Grade::where('student_id', $id)
            ->whereIn('subject_id', $subjects->pluck('id'))
            ->get();

        // Debug information
        \Log::info('Student Grade View Data:', [
            'student_id' => $id,
            'student_name' => $student->user->full_name,
            'student_grade_level' => $student->grade_level,
            'student_section' => $student->section,
            'automatic_enrollments' => $student->automaticEnrollments->toArray(),
            'subjects_count' => $subjects->count(),
            'subjects' => $subjects->toArray(),
            'grades_count' => $grades->count(),
            'grades' => $grades->toArray()
        ]);

        // If no subjects are found, try to enroll the student automatically
        if ($subjects->isEmpty()) {
            $enrollmentService = app(AutomaticEnrollmentService::class);
            $enrollmentService->enrollStudent($student);
            
            // Refresh the subjects after enrollment
            $subjects = Subject::where('grade_level', $student->grade_level)
                ->whereNull('parent_id')
                ->where('status', 'active')
                ->orderBy('name')
                ->get();
                
            if ($subjects->isEmpty()) {
                return view('teachers.student.grade.show', [
                    'student' => $student,
                    'subjects' => collect(),
                    'grades' => collect(),
                    'error' => 'No subjects found for Grade ' . $student->grade_level . '. Please contact the administrator to set up subjects for this grade level.'
                ]);
            }
        }
            
        return view('teachers.student.grade.show', compact('student', 'subjects', 'grades'));
    }

    /**
     * Show the event page with a list of events.
     */
    public function event()
    {
        // Only show events that are visible to teachers
        $events = Event::where(function($query) {
            $query->where('visibility', 'All')
                  ->orWhere('visibility', 'Teachers');
        })
        ->whereNotIn('visibility', ['Students'])
        ->orderBy('event_date', 'desc')
        ->get();
        
        return view('teachers.event.index', compact('events'));
    }

    /**
     * Search for a student by student_id.
     */
    public function searchStudent(Request $request)
    {
        $search = $request->input('search');
        $student = Student::where('student_id', $search)->first();

        if ($student) {
            $user = User::find($student->user_id);
            return view('teachers.student.search_result', compact('student', 'user'));
        } else {
            return back()->with('error', 'Student not found in the database.');
        }
    }

    /**
     * Show the list of subjects assigned to the authenticated teacher.
     */
    public function index()
    {
        // Get subjects that belong to the authenticated teacher
        $subjects = Subject::where('teacher_id', Auth::id())->get();

        return view('teachers.subject.index', compact('subjects'));
    }

    /**
     * Show a single subject's details.
     */
    public function show($id)
    {
        $student = Student::with('user')->where('user_id', $id)->firstOrFail();
        return view('teachers.student.show', compact('student'));
    }

    /**
     * Show the form for editing a specific student.
     */
    public function edit($id)
    {
        $teacher = auth()->user();
        $section = Section::where('adviser_id', $teacher->id)->first();

        if (!$section) {
            return redirect()->route('teachers.student.index')
                ->with('error', 'You are not assigned to any section. Please contact the administrator.');
        }

        $student = Student::with('user')
            ->where('user_id', $id)
            ->where('section', $section->name)
            ->firstOrFail();

        return view('teachers.student.edit', compact('student', 'section'));
    }

    /**
     * Handle the update of a student.
     */
    public function update(Request $request, $id)
    {
        $teacher = auth()->user();
        $section = Section::where('adviser_id', $teacher->id)->first();

        if (!$section) {
            return redirect()->route('teachers.student.index')
                ->with('error', 'You are not assigned to any section. Please contact the administrator.');
        }

        // Verify the student belongs to the teacher's section
        $student = Student::where('user_id', $id)
            ->where('section', $section->name)
            ->firstOrFail();

        $validated = $request->validate([
            'last_name' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'suffix' => 'nullable|string|max:10',
            'email' => 'required|email|unique:users,email,' . $id,
            'gender' => 'required|string|in:Male,Female,Other',
            'birthdate' => 'required|date',
            'lrn' => 'required|string|unique:students,lrn,' . $id . ',user_id',
            'phone' => 'nullable|string|max:20',
            'street_address' => 'nullable|string|max:255',
            'barangay' => 'nullable|string|max:255',
            'municipality' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
        ]);

        DB::beginTransaction();
        try {
            // Update user record
            $student->user->update([
                'last_name' => $validated['last_name'],
                'first_name' => $validated['first_name'],
                'middle_name' => $validated['middle_name'],
                'suffix' => $validated['suffix'],
                'email' => $validated['email'],
            ]);

            // Update student record using user_id
            Student::where('user_id', $id)->update([
                'gender' => $validated['gender'],
                'birthdate' => $validated['birthdate'],
                'lrn' => $validated['lrn'],
                'phone' => $validated['phone'],
                'street_address' => $validated['street_address'],
                'barangay' => $validated['barangay'],
                'municipality' => $validated['municipality'],
                'province' => $validated['province'],
                'grade_level' => $section->grade_level, // Keep the section's grade level
                'section' => $section->name, // Keep the section name
            ]);

            DB::commit();
            return redirect()->route('teachers.student.index')->with('success', 'Student updated successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to update student. Please try again.');
        }
    }

    /**
     * Show the event creation form.
     */
    public function create()
    {
        return view('teachers.event.create');
    }

    /**
     * Preview the event before saving.
     */
    public function preview(Request $request)
    {
        // Validate the incoming event data
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'event_time' => 'required',
            'location' => 'required|string|max:255',
            'venue_image' => 'nullable|image|max:2048', // optional
        ]);

        // Store the venue image if provided
        if ($request->hasFile('venue_image')) {
            $validated['venue_image'] = $request->file('venue_image')->store('venue_images', 'public');
        }

        return view('teachers.event.preview', compact('validated'));
    }

    /**
     * Store the newly created event.
     */
    public function store(Request $request)
    {
        // Validate the event data
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date',
            'event_time' => 'required|string',
            'location' => 'required|string|max:255',
            'venue_image' => 'nullable|image|max:2048', // optional
        ]);

        // Create a new event
        $event = Event::create([
            'title' => $request->input('title'),
            'description' => $request->input('description'),
            'event_date' => $request->input('event_date'),
            'event_time' => $request->input('event_time'),
            'location' => $request->input('location'),
            'venue_image' => $request->hasFile('venue_image') ? $request->file('venue_image')->store('venue_images', 'public') : null,
            'teacher_id' => Auth::id(),
        ]);

        return redirect()->route('teachers.event.index')->with('success', 'Event created successfully!');
    }

    /**
     * Delete a specific event.
     */
  
    public function subjects()
    {
        $teacher = Auth::user();
        $subjects = Subject::where('teacher_id', $teacher->id)
            ->whereNull('parent_id')  // Only get parent subjects (subject labels)
            ->with(['automaticEnrollments.student'])
            ->get();

        return view('teachers.subjects.index', compact('subjects'));
    }

    public function showSubject($id)
    {
        $subject = Subject::findOrFail($id);
        $enrolledStudents = $this->enrollmentService->getEnrolledStudents($subject);

        return view('teachers.subjects.show', compact('subject', 'enrolledStudents'));
    }

    public function storeStudent(Request $request)
    {
        // Get the teacher's assigned section first
        $teacher = auth()->user();
        $section = Section::where('adviser_id', $teacher->id)->first();

        if (!$section) {
            return redirect()->route('teachers.student.index')
                ->with('error', 'You are not assigned to any section. Please contact the administrator.');
        }

        // Validate the student data
        $request->validate([
            'last_name' => 'required|string|max:255',
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'suffix' => 'nullable|string|max:10',
            'email' => 'required|email|unique:users,email',
            'gender' => 'required|string|in:Male,Female,Other',
            'birthdate' => 'required|date',
            'lrn' => 'required|string|unique:students,lrn',
            'phone' => 'nullable|string|max:20',
            'street_address' => 'nullable|string|max:255',
            'barangay' => 'nullable|string|max:255',
            'municipality' => 'nullable|string|max:255',
            'province' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            // Get the latest student ID and generate the next one
            $latestStudent = Student::orderBy('student_id', 'desc')->first();
            $nextId = $latestStudent ? intval(substr($latestStudent->student_id, 3)) + 1 : 1;
            $studentId = 'STU' . str_pad($nextId, 5, '0', STR_PAD_LEFT);
            
            // Generate temporary password based on student ID
            $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

            // Create user record
            $user = User::create([
                'last_name' => $request->last_name,
                'first_name' => $request->first_name,
                'middle_name' => $request->middle_name,
                'suffix' => $request->suffix,
                'email' => $request->email,
                'username' => $studentId,
                'password' => bcrypt($tempPassword),
                'role' => 'student',
            ]);

            // Create student record with teacher's section and grade level
            Student::create([
                'user_id' => $user->id,
                'student_id' => $studentId,
                'lrn' => $request->lrn,
                'street_address' => $request->street_address,
                'barangay' => $request->barangay,
                'municipality' => $request->municipality,
                'province' => $request->province,
                'phone' => $request->phone,
                'birthdate' => $request->birthdate,
                'gender' => $request->gender,
                'grade_level' => $section->grade_level, // Use section's grade level
                'section' => $section->name, // Use section's name
                'status' => 'active',
            ]);

            DB::commit();
            return redirect()->route('teachers.student.index')
                ->with('success', 'Student added successfully! Temporary password: ' . $tempPassword);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to add student. Please check the information and try again.');
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            
            // Validate the removal reason
            $request = request();
            $validated = $request->validate([
                'removal_reason' => 'required|in:dropped,transferred,graduated'
            ]);
            
            // Update only the student status
            $updated = Student::where('user_id', $id)
                ->update(['status' => $validated['removal_reason']]);
            
            if (!$updated) {
                throw new \Exception('Student not found');
            }
            
            DB::commit();
            return redirect()->route('teachers.student.index')
                ->with('success', 'Student has been marked as ' . ucfirst($validated['removal_reason']) . '. The student record remains in the system for administrative purposes.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('teachers.student.index')
                ->with('error', 'Failed to update student status. Please try again.');
        }
    }

    /**
     * Show the teacher's profile page.
     */
    public function profile()
    {
        $user = Auth::user();
        return view('teachers.profile', compact('user'));
    }

    /**
     * Update the teacher's profile.
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'profile_picture' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'current_password' => ['nullable', 'required_with:new_password'],
            'new_password' => ['nullable', 'min:6', 'confirmed'],
        ]);

        try {
            // Handle profile picture upload
            if ($request->hasFile('profile_picture')) {
                // Delete old profile picture if exists
                if ($user->profile_picture) {
                    $oldPath = storage_path('app/private/' . $user->profile_picture);
                    if (file_exists($oldPath)) {
                        unlink($oldPath);
                    }
                }

                // Store new profile picture in /app/private
                $file = $request->file('profile_picture');
                $filename = time() . '_' . $user->id . '.' . $file->getClientOriginalExtension();

                // Ensure the private directory exists
                $privateDir = storage_path('app/private');
                if (!file_exists($privateDir)) {
                    mkdir($privateDir, 0755, true);
                }

                // Move the file to the private directory
                $file->move($privateDir, $filename);
                $user->profile_picture = $filename;
            }

            // Handle password change
            if ($request->filled('current_password')) {
                if (!Hash::check($request->current_password, $user->password)) {
                    return back()->withErrors(['current_password' => 'The current password is incorrect.']);
                }
                $user->password = Hash::make($validated['new_password']);
            }

            $user->save();

            return back()->with('success', 'Profile updated successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update profile: ' . $e->getMessage());
        }
    }

    /**
     * Remove the teacher's profile picture.
     */
    public function removeProfilePicture()
    {
        $user = Auth::user();

        try {
            if ($user->profile_picture) {
                // Delete the file from private storage
                $filePath = storage_path('app/private/' . $user->profile_picture);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                // Remove from database
                $user->profile_picture = null;
                $user->save();

                return back()->with('success', 'Profile picture removed successfully.');
            }

            return back()->with('error', 'No profile picture to remove.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to remove profile picture: ' . $e->getMessage());
        }
    }

    public function myClass()
    {
        $teacher = auth()->user(); // assuming the teacher is logged in
        $section = $teacher->section;

        if (!$section) {
            return view('teachers.index', [
                'students' => [],
                'error' => 'No section assigned.'
            ]);
        }

        $students = $section->students;

        return view('teachers.index', compact('students'));
    }

    public function storeGrades(Request $request)
    {
        $request->validate([
            'student_id' => 'required|exists:students,user_id',
            'grades' => 'required|array',
            'grades.*.subject_id' => 'required|exists:subjects,id',
            'grades.*.1st' => 'nullable|numeric|min:0|max:100',
            'grades.*.2nd' => 'nullable|numeric|min:0|max:100',
            'grades.*.3rd' => 'nullable|numeric|min:0|max:100',
            'grades.*.4th' => 'nullable|numeric|min:0|max:100',
        ]);

        try {
            DB::beginTransaction();

            foreach ($request->grades as $subjectId => $gradeData) {
                foreach (['1st', '2nd', '3rd', '4th'] as $quarter) {
                    // Find existing grade record
                    $existingGrade = Grade::where([
                        'student_id' => $request->student_id,
                        'subject_id' => $subjectId,
                        'grading_period' => $quarter,
                    ])->first();

                    if (isset($gradeData[$quarter]) && $gradeData[$quarter] !== '') {
                        // If grade is provided, update or create
                        if ($existingGrade) {
                            $existingGrade->update(['grade' => $gradeData[$quarter]]);
                        } else {
                            Grade::create([
                                'student_id' => $request->student_id,
                                'subject_id' => $subjectId,
                                'grading_period' => $quarter,
                                'grade' => $gradeData[$quarter],
                            ]);
                        }
                    } else {
                        // If grade is empty, delete the record if it exists
                        if ($existingGrade) {
                            $existingGrade->delete();
                        }
                    }
                }
            }

            DB::commit();
            return redirect()->back()->with('success', 'Grades saved successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to save grades: ' . $e->getMessage());
        }
    }

    /**
     * Download the CSV template for student import
     */
    public function downloadTemplate()
    {
        $csv = Writer::createFromString('');
        
        // Add headers
        $csv->insertOne([
            'last_name',
            'first_name',
            'middle_name',
            'suffix',
            'email',
            'lrn',
            'street_address',
            'barangay',
            'municipality',
            'province',
            'phone',
            'birthdate',
            'gender'
        ]);

        // Add sample data
        $csv->insertOne([
            'Doe',
            'John',
            'Smith',
            'Jr',
            '<EMAIL>',
            '123456789012',
            '123 Main St',
            'Sample Barangay',
            'Sample Municipality',
            'Sample Province',
            '09123456789',
            '2000-01-01',
            'Male'
        ]);

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="student_update_template.csv"',
        ];

        return response($csv->toString(), 200, $headers);
    }

    /**
     * Import students from CSV file
     */
    public function importStudents(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240' // max 10MB
        ]);

        try {
            DB::beginTransaction();

            $csv = Reader::createFromPath($request->file('csv_file')->getPathname());
            $csv->setHeaderOffset(0);

            $teacher = auth()->user();
            $section = Section::where('adviser_id', $teacher->id)->first();

            if (!$section) {
                throw new \Exception('You are not assigned to any section. Please contact the administrator.');
            }

            $records = $csv->getRecords();
            $updatedCount = 0;
            $createdCount = 0;
            $errors = [];
            $enrollmentService = app(AutomaticEnrollmentService::class);

            foreach ($records as $index => $record) {
                try {
                    // Validate required fields
                    if (empty($record['last_name']) || empty($record['first_name']) || empty($record['email']) || empty($record['lrn'])) {
                        throw new \Exception('Required fields missing');
                    }

                    // Find existing student by LRN
                    $existingStudent = Student::where('lrn', $record['lrn'])->first();
                    
                    if ($existingStudent) {
                        // Update existing student
                        $user = $existingStudent->user;
                        $user->last_name = $record['last_name'];
                        $user->first_name = $record['first_name'];
                        $user->middle_name = $record['middle_name'] ?? $user->middle_name;
                        $user->suffix = $record['suffix'] ?? $user->suffix;
                        $user->email = $record['email'];
                        $user->save();

                        // Convert birthdate from MM/DD/YYYY to YYYY-MM-DD if provided
                        $birthdate = null;
                        if (!empty($record['birthdate'])) {
                            $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                            if (!$date) {
                                throw new \Exception('Invalid birthdate format. Must be MM/DD/YYYY');
                            }
                            $birthdate = $date->format('Y-m-d');
                        }

                        // Update student record using user_id
                        DB::table('students')
                            ->where('user_id', $existingStudent->user_id)
                            ->update([
                                'street_address' => $record['street_address'] ?? $existingStudent->street_address,
                                'barangay' => $record['barangay'] ?? $existingStudent->barangay,
                                'municipality' => $record['municipality'] ?? $existingStudent->municipality,
                                'province' => $record['province'] ?? $existingStudent->province,
                                'phone' => $record['phone'] ?? $existingStudent->phone,
                                'birthdate' => $birthdate ?? $existingStudent->birthdate,
                                'gender' => $record['gender'] ?? $existingStudent->gender,
                                'section' => $section->name,
                                'grade_level' => $section->grade_level,
                                'status' => 'active',
                                'updated_at' => now()
                            ]);

                        // Get the updated student record
                        $updatedStudent = Student::where('user_id', $existingStudent->user_id)->first();
                        
                        // Automatically enroll the student in subjects for their grade level
                        $enrollmentService->enrollStudent($updatedStudent);

                        $updatedCount++;
                    } else {
                        // Create new student
                        // Generate student ID
                        $latestStudent = Student::orderBy('student_id', 'desc')->first();
                        $nextId = $latestStudent ? intval(substr($latestStudent->student_id, 3)) + 1 : 1;
                        $studentId = 'STU' . str_pad($nextId, 5, '0', STR_PAD_LEFT);
                        
                        // Generate temporary password
                        $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

                        // Create user record
                        $user = User::create([
                            'last_name' => $record['last_name'],
                            'first_name' => $record['first_name'],
                            'middle_name' => $record['middle_name'] ?? null,
                            'suffix' => $record['suffix'] ?? null,
                            'email' => $record['email'],
                            'username' => $studentId,
                            'password' => bcrypt($tempPassword),
                            'role' => 'student',
                        ]);

                        // Convert birthdate
                        $birthdate = null;
                        if (!empty($record['birthdate'])) {
                            $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                            if (!$date) {
                                throw new \Exception('Invalid birthdate format. Must be MM/DD/YYYY');
                            }
                            $birthdate = $date->format('Y-m-d');
                        }

                        // Create student record
                        $newStudent = Student::create([
                            'user_id' => $user->id,
                            'student_id' => $studentId,
                            'lrn' => $record['lrn'],
                            'street_address' => $record['street_address'] ?? null,
                            'barangay' => $record['barangay'] ?? null,
                            'municipality' => $record['municipality'] ?? null,
                            'province' => $record['province'] ?? null,
                            'phone' => $record['phone'] ?? null,
                            'birthdate' => $birthdate,
                            'gender' => $record['gender'] ?? null,
                            'grade_level' => $section->grade_level,
                            'section' => $section->name,
                            'status' => 'active',
                        ]);

                        // Enroll new student in subjects
                        $enrollmentService->enrollStudent($newStudent);
                        $createdCount++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            if (count($errors) > 0) {
                DB::rollBack();
                return redirect()->route('teachers.student.index')
                    ->with('error', 'Import failed. Some rows contain invalid data. Please check your CSV file and try again.');
            }

            DB::commit();
            $message = [];
            if ($updatedCount > 0) {
                $message[] = "Updated {$updatedCount} existing students";
            }
            if ($createdCount > 0) {
                $message[] = "Created {$createdCount} new students";
            }
            return redirect()->route('teachers.student.index')
                ->with('success', implode(" and ", $message) . " and enrolled them in subjects for Grade {$section->grade_level}.");

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('teachers.student.index')
                ->with('error', 'Failed to import students. Please check your CSV file format and try again.');
        }
    }

    public function markEventViewed(Event $event)
    {
        try {
            // Check if the event is already viewed
            $viewed = EventView::where('event_id', $event->id)
                             ->where('user_id', auth()->id())
                             ->exists();

            if (!$viewed) {
                // Mark the event as viewed
                EventView::create([
                    'event_id' => $event->id,
                    'user_id' => auth()->id(),
                    'viewed_at' => now()
                ]);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to mark event as viewed'], 500);
        }
    }

    public function syncStudentSubjects()
    {
        try {
            $teacher = auth()->user();
            $section = Section::where('adviser_id', $teacher->id)->first();

            if (!$section) {
                return redirect()->route('teachers.student.grade.index')
                    ->with('error', 'You are not assigned to any section. Please contact the administrator.');
            }

            // Check if section has a grade level
            if (empty($section->grade_level)) {
                return redirect()->route('teachers.student.grade.index')
                    ->with('error', 'Your section does not have a grade level assigned. Please contact the administrator.');
            }

            // Check if there are subjects for this grade level
            $subjects = Subject::where('grade_level', $section->grade_level)
                ->whereNull('parent_id')
                ->where('status', 'active')
                ->get();

            if ($subjects->isEmpty()) {
                return redirect()->route('teachers.student.grade.index')
                    ->with('error', "No subjects found for Grade {$section->grade_level}. Please contact the administrator to set up subjects for this grade level.");
            }

            // Get all students in the section
            $students = Student::where('section', $section->name)
                ->where('status', 'active')
                ->get();

            $enrollmentService = app(AutomaticEnrollmentService::class);
            $enrolledCount = 0;

            foreach ($students as $student) {
                // Update student's grade level to match section
                $student->grade_level = $section->grade_level;
                $student->save();
                
                // Enroll student in subjects
                $enrollmentService->enrollStudent($student);
                $enrolledCount++;
            }

            return redirect()->route('teachers.student.grade.index')
                ->with('success', "Successfully enrolled {$enrolledCount} students in subjects for Grade {$section->grade_level}.");

        } catch (\Exception $e) {
            return redirect()->route('teachers.student.grade.index')
                ->with('error', 'Failed to sync student subjects: ' . $e->getMessage());
        }
    }

    public function deleteGrade(Grade $grade)
    {
        try {
            // Check if the grade belongs to a subject taught by this teacher
            $subject = Subject::where('id', $grade->subject_id)
                ->where('teacher_id', auth()->id())
                ->first();

            if (!$subject) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not authorized to delete this grade.'
                ], 403);
            }

            $grade->delete();

            return response()->json([
                'success' => true,
                'message' => 'Grade deleted successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete grade.'
            ], 500);
        }
    }
}
