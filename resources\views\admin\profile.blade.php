@extends('layouts.app')

@section('content')
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1 text-primary">My Profile</h2>
                <p class="text-muted mb-0 small">Manage your account information</p>
            </div>
            <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary btn-action">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Dashboard
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        <!-- Profile Summary -->
        <div class="profile-summary-compact">
            <div class="profile-summary-content">
                <div class="profile-avatar-compact">
                    @if($user->hasProfilePicture())
                        <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="profile-img-compact">
                    @else
                        <div class="profile-default-compact">
                            {{ strtoupper(substr($user->first_name, 0, 1)) }}{{ strtoupper(substr($user->last_name, 0, 1)) }}
                        </div>
                    @endif
                    <div class="status-compact">
                        <span class="status-dot"></span>
                    </div>
                </div>
                <div class="profile-info-compact">
                    <h4 class="profile-name-compact">{{ $user->full_name }}</h4>
                    <div class="profile-details-compact">
                        <span class="profile-role-compact">
                            <i class="bi bi-shield-check me-1"></i>{{ ucfirst($user->role) }} Account
                        </span>
                        <span class="profile-divider">•</span>
                        <span class="profile-email-compact">
                            <i class="bi bi-envelope me-1"></i>{{ $user->email }}
                        </span>
                        <span class="profile-divider">•</span>
                        <span class="profile-username-compact">
                            <i class="bi bi-person-badge me-1"></i>{{ $user->username ?? 'Not Set' }}
                        </span>
                    </div>
                </div>
                <div class="profile-status-compact">
                    <span class="status-badge-compact">
                        <i class="bi bi-circle-fill me-1"></i>Active
                    </span>
                </div>
            </div>
        </div>

        <!-- Enhanced Profile Form -->
        <div class="profile-form-container">
            <div class="form-header">
                <h3 class="form-title">
                    <i class="bi bi-person-gear me-2"></i>Profile Settings
                </h3>
                <p class="form-subtitle">Update your personal information and profile picture</p>
            </div>

            <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data" class="profile-form">
                @csrf
                @method('PUT')

                <!-- Profile Picture Section -->
                <div class="form-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="bi bi-camera me-2"></i>Profile Picture
                        </h5>
                        <p class="section-subtitle">Upload a professional photo for your profile</p>
                    </div>

                    <div class="picture-upload-area">
                        <div class="picture-preview-container">
                            <div id="profilePicturePreview" class="picture-preview">
                                @if($user->hasProfilePicture())
                                    <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="preview-image">
                                @else
                                    <div class="preview-placeholder">
                                        <i class="bi bi-person-workspace"></i>
                                        <span>No Image</span>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="picture-upload-controls">
                            <div class="upload-input-group">
                                <label class="upload-label">
                                    <i class="bi bi-cloud-upload me-2"></i>Choose Profile Picture
                                </label>
                                <input type="file" name="profile_picture"
                                       class="upload-input @error('profile_picture') is-invalid @enderror"
                                       accept="image/*" id="profilePictureInput">
                                @error('profile_picture')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                                <div class="upload-hint">Maximum 2MB • JPEG, PNG, JPG, GIF</div>
                            </div>

                            <div id="profilePictureActions" class="picture-actions">
                                @if($user->hasProfilePicture())
                                    <button type="button" class="btn btn-outline-danger btn-action btn-sm" id="removeProfilePicture">
                                        <i class="bi bi-trash me-2"></i>
                                        Remove Picture
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Personal Information Section -->
                <div class="form-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="bi bi-person-lines-fill me-2"></i>Personal Information
                        </h5>
                        <p class="section-subtitle">Update your basic profile details</p>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-person me-2"></i>Last Name
                            </label>
                            <input type="text" name="last_name"
                                class="enhanced-input @error('last_name') is-invalid @enderror"
                                value="{{ old('last_name', $user->last_name) }}" required
                                placeholder="Enter your last name">
                            @error('last_name')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-person me-2"></i>First Name
                            </label>
                            <input type="text" name="first_name"
                                class="enhanced-input @error('first_name') is-invalid @enderror"
                                value="{{ old('first_name', $user->first_name) }}" required
                                placeholder="Enter your first name">
                            @error('first_name')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-person me-2"></i>Middle Name
                            </label>
                            <input type="text" name="middle_name"
                                class="enhanced-input @error('middle_name') is-invalid @enderror"
                                value="{{ old('middle_name', $user->middle_name) }}"
                                placeholder="Enter your middle name (optional)">
                            @error('middle_name')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-person me-2"></i>Suffix
                            </label>
                            <input type="text" name="suffix"
                                class="enhanced-input @error('suffix') is-invalid @enderror"
                                value="{{ old('suffix', $user->suffix) }}"
                                placeholder="Jr., Sr., III (optional)">
                            @error('suffix')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group form-group-full">
                            <label class="enhanced-label">
                                <i class="bi bi-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" name="email"
                                class="enhanced-input @error('email') is-invalid @enderror"
                                value="{{ old('email', $user->email) }}" required
                                placeholder="Enter your email address">
                            @error('email')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Password Section -->
                <div class="form-section">
                    <div class="section-header">
                        <h5 class="section-title">
                            <i class="bi bi-shield-lock me-2"></i>Security Settings
                        </h5>
                        <p class="section-subtitle">Update your password to keep your account secure</p>
                    </div>

                    <div class="password-notice">
                        <i class="bi bi-info-circle me-2"></i>
                        Leave password fields empty if you don't want to change your current password
                    </div>

                    <div class="form-grid">
                        <div class="form-group form-group-full">
                            <label class="enhanced-label">
                                <i class="bi bi-key me-2"></i>Current Password
                            </label>
                            <input type="password" name="current_password"
                                class="enhanced-input @error('current_password') is-invalid @enderror"
                                placeholder="Enter your current password">
                            @error('current_password')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-key me-2"></i>New Password
                            </label>
                            <input type="password" name="new_password"
                                class="enhanced-input @error('new_password') is-invalid @enderror"
                                placeholder="Enter new password">
                            @error('new_password')
                                <div class="error-message">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="bi bi-key me-2"></i>Confirm New Password
                            </label>
                            <input type="password" name="new_password_confirmation"
                                class="enhanced-input"
                                placeholder="Confirm new password">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-action">
                        <i class="bi bi-check-circle me-2"></i>
                        Save Changes
                    </button>
                    <button type="button" class="btn btn-secondary btn-action" onclick="window.location.reload()">
                        <i class="bi bi-x-circle me-2"></i>
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Remove Profile Picture Form -->
    <form id="removeProfilePictureForm" action="{{ route('admin.profile.picture.remove') }}" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
    </form>

    <style>
        .profile-picture-container {
            width: 120px;
            height: 120px;
            position: relative;
        }

        .profile-picture-preview {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #e9ecef;
        }

        .profile-picture-placeholder {
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: 3px dashed #dee2e6;
            background-color: #f8f9fa;
        }

        .avatar-lg {
            width: 120px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .avatar-img {
            width: 120px;
            height: 120px;
            object-fit: cover;
        }

        .avatar-title {
            font-size: 24px;
            font-weight: 500;
        }

        .card {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .shadow-sm {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        }

        /* Compact Profile Summary Styles */
        .profile-summary-compact {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out;
        }

        .profile-summary-content {
            display: flex;
            align-items: center;
            padding: 1.25rem 1.5rem;
            gap: 1rem;
            position: relative;
        }

        .profile-summary-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            pointer-events: none;
        }

        .profile-avatar-compact {
            position: relative;
            flex-shrink: 0;
            z-index: 2;
        }

        .profile-img-compact {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .profile-default-compact {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .status-compact {
            position: absolute;
            bottom: 2px;
            right: 2px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #10b981;
            border: 2px solid white;
            display: block;
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.3);
        }

        .profile-info-compact {
            flex: 1;
            z-index: 2;
        }

        .profile-name-compact {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0 0 0.25rem 0;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .profile-details-compact {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .profile-role-compact,
        .profile-email-compact,
        .profile-username-compact {
            display: flex;
            align-items: center;
        }

        .profile-divider {
            color: rgba(255, 255, 255, 0.6);
            font-weight: bold;
        }

        .profile-status-compact {
            z-index: 2;
        }

        .status-badge-compact {
            background: rgba(16, 185, 129, 0.3);
            color: white;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Enhanced Form Styles */
        .profile-form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            animation: slideInUp 0.6s ease-out 0.2s both;
        }

        .form-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .form-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
        }

        .form-subtitle {
            color: #64748b;
            margin: 0;
            font-size: 0.95rem;
        }

        .profile-form {
            padding: 0;
        }

        .form-section {
            padding: 2rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .form-section:last-of-type {
            border-bottom: none;
        }

        .section-header {
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
        }

        .section-subtitle {
            color: #64748b;
            margin: 0;
            font-size: 0.875rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group-full {
            grid-column: 1 / -1;
        }

        .enhanced-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }

        .enhanced-input {
            padding: 0.875rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .enhanced-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .enhanced-input.is-invalid {
            border-color: #ef4444;
            background: #fef2f2;
        }

        .error-message {
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            font-weight: 500;
        }

        /* Picture Upload Styles */
        .picture-upload-area {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
        }

        .picture-preview-container {
            flex-shrink: 0;
        }

        .picture-preview {
            width: 120px;
            height: 120px;
            border-radius: 16px;
            overflow: hidden;
            border: 3px dashed #d1d5db;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
            transition: all 0.3s ease;
        }

        .picture-preview:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preview-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #9ca3af;
            font-size: 0.875rem;
        }

        .preview-placeholder i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .picture-upload-controls {
            flex: 1;
        }

        .upload-input-group {
            margin-bottom: 1rem;
        }

        .upload-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }

        .upload-input {
            padding: 0.875rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: #fafafa;
            width: 100%;
        }

        .upload-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .upload-hint {
            color: #6b7280;
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .picture-actions {
            display: flex;
            gap: 0.75rem;
        }

        .btn-remove {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .btn-remove:hover {
            background: #fee2e2;
            border-color: #fca5a5;
        }

        /* Password Notice */
        .password-notice {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #1e40af;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
        }

        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Form Actions */
        .form-actions {
            padding: 2rem;
            background: #f8fafc;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        /* Button Styling to Match Admin Students/Teachers */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            box-sizing: border-box;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-outline-danger.btn-action {
            background: #ffffff;
            color: #dc2626;
            border: 2px solid #fecaca;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-outline-danger.btn-action:hover {
            background: #fef2f2;
            border-color: #fca5a5;
            color: #b91c1c;
            box-shadow: 0 4px 8px rgba(220, 38, 38, 0.1);
            transform: translateY(-2px);
        }

        .btn-sm.btn-action {
            padding: 8px 16px;
            font-size: 13px;
            min-width: 120px;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .profile-summary-content {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
                padding: 1rem;
            }

            .profile-details-compact {
                flex-direction: column;
                gap: 0.25rem;
            }

            .profile-divider {
                display: none;
            }

            .form-header {
                padding: 1.5rem;
            }

            .form-section {
                padding: 1.5rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .picture-upload-area {
                flex-direction: column;
                gap: 1rem;
                align-items: center;
            }

            .form-actions {
                padding: 1.5rem;
                flex-direction: column;
            }

            .btn-action {
                width: 100%;
                justify-content: center;
                min-width: auto;
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .profile-summary-header {
            padding: 2rem 2rem 1rem;
            color: white;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .profile-summary-avatar {
            position: relative;
            flex-shrink: 0;
        }

        .profile-summary-img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .profile-summary-default-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .profile-summary-status {
            position: absolute;
            bottom: 5px;
            right: 5px;
        }

        .status-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            display: block;
        }

        .status-indicator.active {
            background-color: #10b981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
        }

        .profile-summary-info {
            flex: 1;
        }

        .profile-summary-name {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .profile-summary-role {
            font-size: 1.1rem;
            margin: 0 0 1rem 0;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
        }

        .profile-summary-badges {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .profile-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .profile-badge.active {
            background: rgba(59, 130, 246, 0.3);
        }

        .profile-summary-details {
            background: white;
            padding: 1.5rem 2rem 2rem;
            position: relative;
            z-index: 2;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 1.25rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .detail-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .detail-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .detail-item:hover::before {
            opacity: 1;
        }

        .detail-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .detail-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .detail-value {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.4;
        }

        @media (max-width: 768px) {
            .profile-summary-card {
                margin-bottom: 1.5rem;
            }

            .profile-summary-header {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
                padding: 1.5rem 1rem 0.75rem;
            }

            .profile-summary-img,
            .profile-summary-default-avatar {
                width: 70px;
                height: 70px;
            }

            .profile-summary-name {
                font-size: 1.4rem;
            }

            .profile-summary-details {
                padding: 1rem;
            }

            .detail-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .detail-item {
                padding: 1.25rem;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const profilePictureInput = document.getElementById('profilePictureInput');
            const profilePicturePreview = document.getElementById('profilePicturePreview');
            const profilePictureActions = document.getElementById('profilePictureActions');
            const removeProfilePictureBtn = document.getElementById('removeProfilePicture');

            let originalImageExists = {{ $user->hasProfilePicture() ? 'true' : 'false' }};
            let hasNewImage = false;

            // Handle file input change for preview
            if (profilePictureInput) {
                profilePictureInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            // Update preview
                            profilePicturePreview.innerHTML = `<img src="${e.target.result}" alt="Profile Picture Preview" class="preview-image">`;

                            // Update profile summary avatar
                            updateProfileSummaryAvatar(e.target.result);

                            // Update button to "Clear Image"
                            hasNewImage = true;
                            updateActionButton();
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // Reset to original state if no file selected
                        resetToOriginalState();
                    }
                });
            }

            function updateActionButton() {
                if (hasNewImage) {
                    // Show "Clear Image" button when new image is selected
                    profilePictureActions.innerHTML = `
                        <button type="button" class="btn btn-outline-danger btn-action btn-sm" id="clearImageBtn">
                            <i class="bi bi-x-circle me-2"></i>
                            Clear Image
                        </button>
                    `;

                    // Add event listener to clear button
                    document.getElementById('clearImageBtn').addEventListener('click', function() {
                        clearSelectedImage();
                    });
                } else if (originalImageExists) {
                    // Show "Remove Picture" button for existing images
                    profilePictureActions.innerHTML = `
                        <button type="button" class="btn btn-outline-danger btn-action btn-sm" id="removeProfilePicture">
                            <i class="bi bi-trash me-2"></i>
                            Remove Picture
                        </button>
                    `;

                    // Add event listener to remove button
                    document.getElementById('removeProfilePicture').addEventListener('click', function() {
                        if (confirm('Are you sure you want to remove your profile picture?')) {
                            document.getElementById('removeProfilePictureForm').submit();
                        }
                    });
                } else {
                    // No buttons needed if no image exists and no new image selected
                    profilePictureActions.innerHTML = '';
                }
            }

            function clearSelectedImage() {
                // Clear the file input
                profilePictureInput.value = '';
                hasNewImage = false;

                // Reset to original state
                resetToOriginalState();
            }

            function resetToOriginalState() {
                hasNewImage = false;

                // Reset preview to original state
                if (originalImageExists) {
                    profilePicturePreview.innerHTML = `
                        <img src="{{ $user->profile_picture_url }}" alt="Profile Picture" class="preview-image">
                    `;
                    // Reset profile summary to original image
                    updateProfileSummaryAvatar('{{ $user->profile_picture_url }}');
                } else {
                    profilePicturePreview.innerHTML = `
                        <div class="preview-placeholder">
                            <i class="bi bi-person-workspace"></i>
                            <span>No Image</span>
                        </div>
                    `;
                    // Reset profile summary to default avatar
                    updateProfileSummaryAvatar(null);
                }

                // Update button
                updateActionButton();
            }

            // Update profile summary avatar when image changes
            function updateProfileSummaryAvatar(imageSrc) {
                const summaryAvatar = document.querySelector('.profile-avatar-compact');
                if (summaryAvatar) {
                    if (imageSrc) {
                        summaryAvatar.innerHTML = `
                            <img src="${imageSrc}" alt="Profile Picture" class="profile-img-compact">
                            <div class="status-compact">
                                <span class="status-dot"></span>
                            </div>
                        `;
                    } else {
                        summaryAvatar.innerHTML = `
                            <div class="profile-default-compact">
                                {{ strtoupper(substr($user->first_name, 0, 1)) }}{{ strtoupper(substr($user->last_name, 0, 1)) }}
                            </div>
                            <div class="status-compact">
                                <span class="status-dot"></span>
                            </div>
                        `;
                    }
                }
            }

            // Handle remove profile picture (for existing functionality)
            if (removeProfilePictureBtn) {
                removeProfilePictureBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to remove your profile picture?')) {
                        document.getElementById('removeProfilePictureForm').submit();
                    }
                });
            }
        });
    </script>
@endsection