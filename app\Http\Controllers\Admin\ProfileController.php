<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    public function show()
    {
        $user = Auth::user();
        return view('admin.profile', compact('user'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'last_name' => ['required', 'string', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'middle_name' => ['nullable', 'string', 'max:255'],
            'suffix' => ['nullable', 'string', 'max:10'],
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'profile_picture' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
            'current_password' => ['nullable', 'required_with:new_password'],
            'new_password' => ['nullable', 'min:6', 'confirmed'],
        ]);

        $user->last_name = $validated['last_name'];
        $user->first_name = $validated['first_name'];
        $user->middle_name = $validated['middle_name'];
        $user->suffix = $validated['suffix'];
        $user->email = $validated['email'];

        // Handle profile picture upload
        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture if exists
            if ($user->profile_picture) {
                $oldPath = storage_path('app/private/' . $user->profile_picture);
                if (file_exists($oldPath)) {
                    unlink($oldPath);
                }
            }

            // Store new profile picture in /app/private
            $file = $request->file('profile_picture');
            $filename = time() . '_' . $user->id . '.' . $file->getClientOriginalExtension();

            // Ensure the private directory exists
            $privateDir = storage_path('app/private');
            if (!file_exists($privateDir)) {
                mkdir($privateDir, 0755, true);
            }

            // Move the file to the private directory
            $file->move($privateDir, $filename);
            $user->profile_picture = $filename;
        }

        if ($request->filled('current_password')) {
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'The current password is incorrect.']);
            }
            $user->password = Hash::make($validated['new_password']);
        }

        $user->save();

        return back()->with('success', 'Profile updated successfully.');
    }

    public function removeProfilePicture()
    {
        $user = Auth::user();

        try {
            if ($user->profile_picture) {
                // Delete the file from private storage
                $filePath = storage_path('app/private/' . $user->profile_picture);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                // Remove from database
                $user->profile_picture = null;
                $user->save();

                return back()->with('success', 'Profile picture removed successfully.');
            }

            return back()->with('error', 'No profile picture to remove.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to remove profile picture: ' . $e->getMessage());
        }
    }
} 