@extends('layouts.app')

@section('content')
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1 text-primary">Event Management</h2>
                <p class="text-muted mb-0 small">Manage and monitor school events and activities</p>
            </div>
            <div class="d-flex gap-3">
                <div class="search-container">
                    <div class="search-input-wrapper">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" name="search" class="search-input" placeholder="Search events by title, location, or status..."
                            value="{{ request('search') }}">
                        <div class="search-loading-spinner" style="display: none;">
                            <div class="spinner"></div>
                        </div>
                        <button type="button" class="search-clear-btn" style="display: none;">
                            <i class="bi bi-x-circle-fill"></i>
                        </button>
                    </div>
                    <div class="search-suggestions" style="display: none;">
                        <!-- Dynamic search suggestions will appear here -->
                    </div>
                </div>
                <a href="{{ route('admin.events.create') }}" class="btn btn-primary btn-action">
                    <i class="bi bi-plus-lg me-2"></i>
                    Add Event
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-circle-fill me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary">Event Details</th>
                                <th class="text-primary">Schedule</th>
                                <th class="text-primary">Location</th>
                                <th class="text-primary">Visibility</th>
                                <th class="text-primary">Status</th>
                                <th class="text-primary text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($events as $event)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-calendar-event text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $event->title }}</div>
                                                <div class="small text-muted">{{ Str::limit($event->description, 50) }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-clock text-info"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $event->event_date->format('M d, Y') }}</div>
                                                <div class="small text-muted">
                                                    {{ \Carbon\Carbon::parse($event->start_time)->format('h:i A') }} - 
                                                    {{ \Carbon\Carbon::parse($event->end_time)->format('h:i A') }}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-geo-alt text-warning"></i>
                                            </div>
                                            <span class="fw-medium">{{ $event->location }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-success bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-eye text-success"></i>
                                            </div>
                                            <span class="fw-medium">{{ $event->visibility }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $event->status === 'Upcoming' ? 'success' : ($event->status === 'Completed' ? 'info' : 'danger') }}">
                                            <i class="bi bi-{{ $event->status === 'Upcoming' ? 'clock' : ($event->status === 'Completed' ? 'check-circle' : 'x-circle') }} me-1"></i>
                                            {{ $event->status }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-end gap-2">
                                            <a href="{{ route('admin.events.edit', $event->event_id) }}"
                                                class="btn btn-sm btn-outline-primary btn-table-action" title="Edit Event">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="{{ route('admin.events.show', $event->event_id) }}"
                                                class="btn btn-sm btn-outline-info btn-table-action" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <form action="{{ route('admin.events.destroy', $event->event_id) }}" method="POST"
                                                class="d-inline delete-event-form">
                                                @csrf
                                                @method('DELETE')
                                                <button type="button" class="btn btn-sm btn-outline-danger btn-table-action delete-event-btn"
                                                    data-bs-toggle="modal" data-bs-target="#deleteEventModal"
                                                    data-event-id="{{ $event->event_id }}"
                                                    data-event-title="{{ $event->title }}"
                                                    title="Delete Event">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-calendar-x fs-2 d-block mb-2"></i>
                                            No events found
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted small">
                        Showing {{ $events->firstItem() ?? 0 }} to {{ $events->lastItem() ?? 0 }} of
                        {{ $events->total() ?? 0 }} entries
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        @if($events->hasPages())
                            @if($events->onFirstPage())
                                <button class="btn btn-secondary btn-action pagination-btn" disabled>
                                    <i class="bi bi-chevron-left me-2"></i>
                                    Previous
                                </button>
                            @else
                                <a href="{{ $events->previousPageUrl() }}" class="btn btn-secondary btn-action pagination-btn">
                                    <i class="bi bi-chevron-left me-2"></i>
                                    Previous
                                </a>
                            @endif

                            @if($events->hasMorePages())
                                <a href="{{ $events->nextPageUrl() }}" class="btn btn-primary btn-action pagination-btn">
                                    Next
                                    <i class="bi bi-chevron-right ms-2"></i>
                                </a>
                            @else
                                <button class="btn btn-secondary btn-action pagination-btn" disabled>
                                    Next
                                    <i class="bi bi-chevron-right ms-2"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="deleteEventModalLabel">Confirm Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="avatar-sm bg-danger bg-opacity-10 rounded-circle mx-auto mb-3">
                            <i class="bi bi-exclamation-triangle-fill text-danger fs-4"></i>
                        </div>
                        <h5 class="mb-1">Are you sure?</h5>
                        <p class="text-muted mb-0">You are about to delete <span class="fw-bold" id="eventTitle"></span>. This action cannot be undone.</p>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-secondary btn-action" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-danger btn-action" id="confirmDeleteBtn">
                        <i class="bi bi-trash me-2"></i>
                        Delete Event
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Button Styling to Match Admin Students/Teachers */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            box-sizing: border-box;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-danger.btn-action {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-danger.btn-action:hover {
            background: linear-gradient(135deg, #b91c1c, #dc2626);
            box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        /* Table Action Buttons */
        .btn-table-action {
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            transition: var(--transition);
            min-width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-table-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .card.shadow-sm {
            background-color: #f8f9fa;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-sm i {
            font-size: 1rem;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
        }

        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
        }

        .alert-success {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .btn-xs {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            min-width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-xs i {
            font-size: 0.75rem;
        }

        .pagination-btn {
            min-width: 120px;
        }

        .pagination-btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Enhanced Search Container */
        .search-container {
            position: relative;
            width: 320px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 4px;
            height: 42px;
            box-sizing: border-box;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .search-input-wrapper:hover {
            border-color: #cbd5e0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-input-wrapper:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-icon {
            color: #64748b;
            font-size: 16px;
            margin-left: 12px;
            margin-right: 8px;
            transition: var(--transition);
            z-index: 2;
        }

        .search-input-wrapper:focus-within .search-icon {
            color: var(--primary);
            transform: scale(1.1);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 12px 8px;
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            height: 100%;
            box-sizing: border-box;
            placeholder-color: #94a3b8;
        }

        .search-input::placeholder {
            color: #94a3b8;
            font-weight: 400;
        }

        .search-loading-spinner {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #64748b;
            font-size: 16px;
            margin-right: 8px;
            padding: 4px;
            border-radius: 50%;
            transition: var(--transition);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn:hover {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

        /* Search Suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            z-index: 1000;
            margin-top: 4px;
            max-height: 300px;
            overflow-y: auto;
            backdrop-filter: blur(8px);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .btn-action {
                padding: 10px 16px;
                font-size: 13px;
                min-width: auto;
            }

            .btn-table-action {
                padding: 6px 8px;
                min-width: 32px;
                height: 32px;
                font-size: 11px;
            }

            .pagination-btn {
                min-width: 100px;
                padding: 8px 12px;
                font-size: 12px;
            }

            /* Enhanced Search Mobile Responsive */
            .search-container {
                width: 100%;
                max-width: 280px;
            }

            .search-input-wrapper {
                border-radius: 12px;
                padding: 4px;
                height: 44px;
                box-sizing: border-box;
            }

            .search-input {
                padding: 10px 8px;
                font-size: 14px;
                height: 100%;
            }

            .search-input::placeholder {
                font-size: 13px;
            }

            .search-icon {
                font-size: 14px;
                margin-left: 10px;
                margin-right: 6px;
            }

            .search-clear-btn {
                font-size: 14px;
                margin-right: 6px;
            }

            .search-suggestions {
                border-radius: 10px;
                max-height: 250px;
            }
        }

        @media (max-width: 576px) {
            .search-container {
                max-width: 240px;
            }

            .search-input::placeholder {
                content: "Search events...";
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const searchInput = document.querySelector('.search-input');
            const searchContainer = document.querySelector('.search-container');
            const loadingSpinner = document.querySelector('.search-loading-spinner');
            const clearBtn = document.querySelector('.search-clear-btn');
            const searchSuggestions = document.querySelector('.search-suggestions');

            const searchTimeout = 150;
            let timeoutId;
            let currentRequest = null;

            // Show/hide clear button based on input value
            function toggleClearButton() {
                if (searchInput.value.trim()) {
                    clearBtn.style.display = 'flex';
                } else {
                    clearBtn.style.display = 'none';
                }
            }

            // Show loading spinner
            function showLoading() {
                loadingSpinner.style.display = 'flex';
                clearBtn.style.display = 'none';
            }

            // Hide loading spinner
            function hideLoading() {
                loadingSpinner.style.display = 'none';
                toggleClearButton();
            }

            // Clear search
            clearBtn.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.focus();
                toggleClearButton();

                // Clear search and show all rows
                performRealTimeSearch();
            });

            // Handle input events - Real-time search
            searchInput.addEventListener('input', function () {
                clearTimeout(timeoutId);
                toggleClearButton();

                const searchValue = this.value.trim();

                // Show loading for longer searches
                if (searchValue.length > 2) {
                    showLoading();
                }

                // Perform real-time search with slight delay for better UX
                timeoutId = setTimeout(() => {
                    hideLoading();
                    performRealTimeSearch();
                }, 100); // Much faster response time
            });

            // Real-time search function - shows/hides rows without changing content
            function performRealTimeSearch() {
                const searchTerm = searchInput.value.trim().toLowerCase();
                const tableBody = document.querySelector('tbody');
                const rows = tableBody.querySelectorAll('tr');
                let visibleCount = 0;

                rows.forEach(row => {
                    // Skip the "no events found" row
                    if (row.querySelector('td[colspan]')) {
                        return;
                    }

                    const cells = row.querySelectorAll('td');
                    let rowText = '';

                    // Combine all cell text content for searching
                    cells.forEach(cell => {
                        // Skip the actions column (last column)
                        if (cell !== cells[cells.length - 1]) {
                            rowText += cell.textContent.toLowerCase() + ' ';
                        }
                    });

                    // Show/hide row based on search match
                    if (!searchTerm || rowText.includes(searchTerm)) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show "no results" message if no rows are visible
                showNoResultsMessage(visibleCount, searchTerm);

                // Update stats display
                updateSearchStats(visibleCount, searchTerm);
            }

            // Show no results message
            function showNoResultsMessage(visibleCount, searchTerm) {
                const tableBody = document.querySelector('tbody');
                let noResultsRow = tableBody.querySelector('.no-results-row');

                if (visibleCount === 0 && searchTerm) {
                    if (!noResultsRow) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-search fs-1 mb-2 d-block"></i>
                                    <p class="mb-0">No events found matching "${searchTerm}"</p>
                                </div>
                            </td>
                        `;
                        tableBody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }

            // Update search statistics
            function updateSearchStats(visibleCount, searchTerm) {
                const statsElement = document.querySelector('.text-muted.small');
                if (statsElement) {
                    if (searchTerm) {
                        statsElement.textContent = `Showing ${visibleCount} events matching "${searchTerm}"`;
                    } else {
                        // Reset to original pagination text when no search
                        const totalEvents = document.querySelectorAll('tbody tr:not(.no-results-row)').length;
                        statsElement.textContent = `Showing ${totalEvents} events`;
                    }
                }
            }

            // Delete Event Modal Functionality
            const deleteModal = document.getElementById('deleteEventModal');
            const eventTitleElement = document.getElementById('eventTitle');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            let currentForm = null;

            // Function to attach delete button event listeners
            function attachDeleteButtonListeners() {
                document.querySelectorAll('.delete-event-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const eventTitle = this.getAttribute('data-event-title');
                        eventTitleElement.textContent = eventTitle;
                        currentForm = this.closest('form');
                    });
                });
            }

            // Initial attachment of delete button listeners
            attachDeleteButtonListeners();

            // When confirm delete is clicked
            confirmDeleteBtn.addEventListener('click', function() {
                if (currentForm) {
                    currentForm.submit();
                }
            });

            // Reset form reference when modal is closed
            deleteModal.addEventListener('hidden.bs.modal', function () {
                currentForm = null;
            });

            // Initialize
            toggleClearButton();
        });
    </script>
@endsection 