@extends('layouts.app')

@section('content')
<div class="container-fluid py-3">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Header Section -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 class="mb-1 text-primary fw-bold">Edit Section</h4>
                    <p class="text-muted mb-0">Update section information for {{ $section->grade_level }}</p>
                </div>
                <div>
                    <a href="{{ route('admin.sections.index') }}" class="btn btn-secondary btn-action">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to Sections
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="card shadow-lg border-0">
                <div class="card-body p-4">
                    <form action="{{ route('admin.sections.update', $section) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="row g-4">
                            <input type="hidden" name="grade_level" value="{{ $section->grade_level }}">

                            <!-- Info Alert -->
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Editing section for {{ $section->grade_level }}
                                </div>
                            </div>

                            <!-- Section Name -->
                            <div class="col-md-6">
                                <label for="name" class="form-label">Section Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror"
                                       id="name" name="name" value="{{ old('name', $section->name) }}" required>
                                <div class="form-text">Enter a unique name for this section (e.g., Section A, Section B)</div>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Section ID -->
                            <div class="col-md-6">
                                <label for="section_id" class="form-label">Section ID</label>
                                <input type="text" class="form-control"
                                       id="section_id" value="{{ $section->section_id }}" readonly>
                                <div class="form-text">Section ID cannot be modified as it is automatically generated.</div>
                            </div>

                            <!-- Section Adviser -->
                            <div class="col-md-12">
                                <label for="adviser_id" class="form-label">Section Adviser <span class="text-danger">*</span></label>
                                <select name="adviser_id" id="adviser_id" class="form-select @error('adviser_id') is-invalid @enderror" required>
                                    <option value="">Select Adviser</option>
                                    @if($section->adviser_id)
                                        <!-- Include current adviser even if they would normally be filtered out -->
                                        <option value="{{ $section->adviser_id }}" {{ old('adviser_id', $section->adviser_id) == $section->adviser_id ? 'selected' : '' }}>
                                            {{ $section->adviser->last_name }}, {{ $section->adviser->first_name }} (Current)
                                        </option>
                                    @endif
                                    @forelse($teachers as $teacher)
                                        @if($teacher->id != $section->adviser_id)
                                            <option value="{{ $teacher->id }}" {{ old('adviser_id', $section->adviser_id) == $teacher->id ? 'selected' : '' }}>
                                                {{ $teacher->last_name }}, {{ $teacher->first_name }}
                                            </option>
                                        @endif
                                    @empty
                                        @if(!$section->adviser_id)
                                            <option value="" disabled>No available teachers (all teachers are already assigned to sections)</option>
                                        @endif
                                    @endforelse
                                </select>
                                @if($teachers->isEmpty() && !$section->adviser_id)
                                    <div class="form-text text-warning">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        All teachers are currently assigned as advisers to other sections.
                                    </div>
                                @else
                                    <div class="form-text">Select a teacher to be the adviser of this section</div>
                                @endif
                                @error('adviser_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Description -->
                            <div class="col-md-12">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control @error('description') is-invalid @enderror"
                                          id="description" name="description" rows="3"
                                          placeholder="Enter any additional information about this section">{{ old('description', $section->description) }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="d-flex justify-content-end gap-3">
                                    <a href="{{ route('admin.sections.index') }}" class="btn btn-secondary btn-action">
                                        <i class="bi bi-x-circle me-2"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-action">
                                        <i class="bi bi-save me-2"></i>
                                        Update Section
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* CSS Variables */
    :root {
        --primary: #6366f1;
        --primary-light: #818cf8;
        --primary-dark: #4f46e5;
        --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .form-label {
        font-weight: 500;
        color: #5A5A89;
    }
    .form-control, .form-select {
        border-radius: 0.5rem;
        border-color: #E5E5EF;
        padding: 0.6rem 1rem;
    }
    .form-control:focus, .form-select:focus {
        border-color: #6f4ef2;
        box-shadow: 0 0 0 0.2rem rgba(111, 78, 242, 0.15);
    }
    .invalid-feedback {
        font-size: 0.875rem;
    }

    /* Button Styling to Match Admin Students/Teachers */
    .btn-action {
        display: inline-flex;
        align-items: center;
        padding: 10px 16px;
        border-radius: 10px;
        font-weight: 500;
        font-size: 14px;
        text-decoration: none;
        transition: var(--transition);
        border: none;
        cursor: pointer;
        min-width: 130px;
        height: 42px;
        box-sizing: border-box;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .btn-primary.btn-action {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: white;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    .btn-primary.btn-action:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary));
        box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
        transform: translateY(-2px);
        color: white;
    }

    .btn-secondary.btn-action {
        background: #ffffff;
        color: #64748b;
        border: 2px solid #e2e8f0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .btn-secondary.btn-action:hover {
        background: #f8fafc;
        border-color: #cbd5e0;
        color: #374151;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .btn-action {
            padding: 10px 16px;
            font-size: 13px;
            min-width: auto;
        }
    }
</style>
@endsection