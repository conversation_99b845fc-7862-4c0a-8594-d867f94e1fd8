@extends('layouts.teacherApp')

@section('content')
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">Student Grades</h2>
            <p class="text-muted mb-0">Manage and view student grades</p>
        </div>
        <div class="d-flex gap-2">
            <form action="{{ route('student.grade.sync') }}" method="POST" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-arrow-repeat me-1"></i> Sync Subjects
                </button>
            </form>
            <div class="input-group shadow-sm" style="width: 250px;">
                <span class="input-group-text bg-white border-end-0">
                    <i class="bi bi-search text-muted"></i>
                </span>
                <input type="text" name="search" class="form-control border-start-0" placeholder="Search students..."
                    value="{{ request('search') }}">
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(isset($error))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            {{ $error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @else
        <!-- Students List -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary">Student ID</th>
                                <th class="text-primary">Name</th>
                                <th class="text-primary">Section</th>
                                <th class="text-primary">Remarks</th>
                                <th class="text-primary text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($students as $student)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person-badge text-primary"></i>
                                            </div>
                                            <span class="fw-medium">{{ $student->student_id }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person text-info"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $student->last_name }}, {{ $student->first_name }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary bg-opacity-10 text-primary">
                                            {{ $student->section }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $latestQuarter = $student->student->grades->sortByDesc('grading_period')->first();
                                            if ($latestQuarter) {
                                                // Get all grades for the latest quarter
                                                $quarterGrades = $student->student->grades
                                                    ->where('grading_period', $latestQuarter->grading_period)
                                                    ->pluck('grade')
                                                    ->filter();
                                                
                                                // Calculate average if there are grades
                                                $average = $quarterGrades->isNotEmpty() ? $quarterGrades->avg() : null;
                                                $remarks = $average !== null ? ($average >= 75 ? 'Passed' : 'Failed') : 'Not Graded';
                                                $badgeClass = $average !== null ? ($average >= 75 ? 'success' : 'danger') : 'secondary';
                                            } else {
                                                $remarks = 'Not Graded';
                                                $badgeClass = 'secondary';
                                            }
                                        @endphp
                                        <span class="badge bg-{{ $badgeClass }}">
                                            {{ $remarks }}
                                        </span>
                                        @if($latestQuarter)
                                            <small class="text-muted d-block mt-1">
                                                {{ $latestQuarter->grading_period }} Quarter
                                                @if($average !== null)
                                                    ({{ number_format($average, 2) }})
                                                @endif
                                            </small>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        <a href="{{ route('teachers.student.grade.show', $student->user_id) }}" 
                                           class="btn btn-primary btn-sm">
                                            <i class="bi bi-journal-text me-1"></i>
                                            View Grades
                                        </a>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-people fs-2 d-block mb-2"></i>
                                            No students found
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted small">
                        Showing {{ $students->firstItem() ?? 0 }} to {{ $students->lastItem() ?? 0 }} of
                        {{ $students->total() ?? 0 }} entries
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        @if($students->hasPages())
                            @if($students->onFirstPage())
                                <button class="btn btn-outline-secondary pagination-btn" disabled>
                                    <i class="bi bi-chevron-left"></i> Previous
                                </button>
                            @else
                                <a href="{{ $students->previousPageUrl() }}" class="btn btn-outline-secondary pagination-btn">
                                    <i class="bi bi-chevron-left"></i> Previous
                                </a>
                            @endif

                            @if($students->hasMorePages())
                                <a href="{{ $students->nextPageUrl() }}" class="btn btn-outline-primary pagination-btn">
                                    Next <i class="bi bi-chevron-right"></i>
                                </a>
                            @else
                                <button class="btn btn-outline-secondary pagination-btn" disabled>
                                    Next <i class="bi bi-chevron-right"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>

<!-- Custom Styling -->
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.avatar-sm i {
    font-size: 1rem;
}

.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
}

.pagination-btn {
    min-width: 100px;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}
</style>

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.querySelector('input[name="search"]');
    const searchTimeout = 500; // milliseconds
    let timeoutId;

    searchInput.addEventListener('input', function() {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
            const currentUrl = new URL(window.location.href);
            const searchValue = this.value.trim();

            if (searchValue) {
                currentUrl.searchParams.set('search', searchValue);
            } else {
                currentUrl.searchParams.delete('search');
            }

            window.location.href = currentUrl.toString();
        }, searchTimeout);
    });
});
</script>
@endsection
