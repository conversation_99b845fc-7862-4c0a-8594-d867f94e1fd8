<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use App\Models\User;

class LoginController extends Controller
{
    public function username() {
        return 'username';
    }

    public function showLoginForm()
    {
        return view('auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ], [
            'username.required' => 'Please enter your ID Number.',
            'password.required' => 'Please enter your password.',
        ]);

        // Check if the username exists
        $user = User::where('username', $request->username)->first();
        if (!$user) {
            return back()
                ->withInput($request->only('username', 'remember'))
                ->withErrors([
                    'username' => 'The ID number you entered is incorrect.',
                ]);
        }

        // If username exists, try to authenticate
        if (!Auth::attempt($credentials, $request->boolean('remember'))) {
            return back()
                ->withInput($request->only('username', 'remember'))
                ->withErrors([
                    'password' => 'The password you entered is incorrect.',
                ]);
        }

        $request->session()->regenerate();

        // Get the authenticated user
        $user = Auth::user();

        // Check student status before allowing access
        if ($user->role === 'student') {
            if (!$user->student || !$this->isStudentActive($user->student->status)) {
                // Log the user out immediately
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();

                return back()
                    ->withInput($request->only('username', 'remember'))
                    ->withErrors([
                        'access_denied' => 'Your student account has been deactivated. Please contact the administration for assistance.',
                    ]);
            }
        }
        switch ($user->role) {
            case 'admin':
                return redirect()->intended(route('admin.dashboard'));
            case 'teacher':
                return redirect()->intended(route('teachers.dashboard'));
            case 'student':
                return redirect()->intended(route('student.gradebook'));
            case 'principal':
                return redirect()->intended(route('admin.dashboard'));
            default:
                return redirect()->intended(route('admin.dashboard'));
        }
    }

    /**
     * Check if student status allows system access
     *
     * @param string|null $status
     * @return bool
     */
    private function isStudentActive(?string $status): bool
    {
        // Only 'active' status allows system access
        // All other statuses (inactive, dropped, transferred, graduated) deny access
        return $status === 'active';
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect()->route('login');
    }
}
