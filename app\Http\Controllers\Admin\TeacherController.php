<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Teacher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\EventView;
use League\Csv\Reader;
use League\Csv\Writer;

class TeacherController extends Controller
{
    public function index(Request $request)
    {
        $query = User::where('role', 'teacher')
            ->with('teacher')
            ->orderBy('last_name')
            ->orderBy('first_name');

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('middle_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('teacher', function($q) use ($search) {
                      $q->where('employee_id', 'like', "%{$search}%");
                  });
            });
        }

        // Apply grade level filter
        if ($request->filled('grade_level')) {
            $gradeLevel = $request->grade_level;
            $query->where(function($q) use ($gradeLevel) {
                // Filter by teachers who teach subjects in this grade level
                $q->whereHas('subjects', function($subjectQuery) use ($gradeLevel) {
                    $subjectQuery->where('grade_level', $gradeLevel);
                })
                // OR filter by teachers who are advisers of sections in this grade level
                ->orWhereHas('teacher', function($teacherQuery) use ($gradeLevel) {
                    $teacherQuery->whereHas('section', function($sectionQuery) use ($gradeLevel) {
                        $sectionQuery->where('grade_level', $gradeLevel);
                    });
                });
            });
        }

        $teachers = $query->paginate(10)->withQueryString();

        return view('admin.teachers.index', compact('teachers'));
    }

    public function create()
    {
        return view('admin.teachers.create');
    }

    public function store(Request $request)
    {
        // Check if email already exists
        if (User::where('email', $request->email)->exists()) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'A user with this email address already exists.');
        }

        // Get the latest teacher ID and generate the next one
        $latestTeacher = Teacher::orderBy('user_id', 'desc')->first();
        $nextId = $latestTeacher ? intval(substr($latestTeacher->employee_id, 3)) + 1 : 1;
        $employeeId = 'TCH' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

        // Generate temporary password
        $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

        // Create user record

        $user = new User();
        $user->last_name = $request->last_name;
        $user->first_name = $request->first_name;
        $user->middle_name = $request->middle_name;
        $user->suffix = $request->suffix;
        $user->email = $request->email;
        $user->username = $employeeId;
        $user->password = bcrypt($tempPassword);
        $user->role = 'teacher';
        $user->save();

        // Create teacher record
        $teacher = new Teacher();
        $teacher->user_id = $user->id;
        $teacher->employee_id = $employeeId;
        $teacher->street_address = $request->street_address;
        $teacher->barangay = $request->barangay;
        $teacher->municipality = $request->municipality;
        $teacher->province = $request->province;
        $teacher->phone = $request->phone;
        $teacher->birthdate = $request->birthdate;
        $teacher->gender = $request->gender;
        $teacher->status = 'active';
        $teacher->save();

        return redirect()->route('admin.teachers.index')
            ->with('success', 'Teacher added successfully! Employee ID: ' . $employeeId . '. Temporary password: ' . $tempPassword);
    }

    public function edit($id)
    {
        $user = User::with('teacher')->findOrFail($id);
        $teacher = $user->teacher ?? new Teacher(['user_id' => $user->id]);
        return view('admin.teachers.edit', compact('teacher', 'user'));
    }

    public function update(Request $request, $id)
    {
        try {
            $user = User::with('teacher')->findOrFail($id);
            
            \Log::info('Updating teacher:', [
                'user_id' => $id,
                'request_data' => $request->all(),
                'address_fields' => [
                    'street_address' => $request->street_address,
                    'barangay' => $request->barangay,
                    'municipality' => $request->municipality,
                    'province' => $request->province
                ]
            ]);

            try {
                $validated = $request->validate([
                    'last_name' => 'required|string|max:255',
                    'first_name' => 'required|string|max:255',
                    'middle_name' => 'nullable|string|max:255',
                    'suffix' => 'nullable|string|max:10',
                    'email' => 'required|email|unique:users,email,' . $id,
                    'password' => 'nullable|string|min:6|confirmed',
                    'phone' => 'nullable|string|max:20',
                    'street_address' => 'nullable|string|max:255',
                    'barangay' => 'nullable|string|max:255',
                    'municipality' => 'nullable|string|max:255',
                    'province' => 'nullable|string|max:255',
                    'birthdate' => 'required|date',
                    'gender' => 'required|string|in:Male,Female,Other',
                    'status' => 'required|in:active,inactive,on_leave'
                ]);
            } catch (\Illuminate\Validation\ValidationException $e) {
                \Log::error('Validation failed:', [
                    'errors' => $e->errors(),
                    'request_data' => $request->all()
                ]);
                throw $e;
            }

            \Log::info('Validated data:', $validated);

            DB::beginTransaction();

            try {
                // Update user record
                $userData = [
                    'last_name' => $validated['last_name'],
                    'first_name' => $validated['first_name'],
                    'middle_name' => $validated['middle_name'],
                    'suffix' => $validated['suffix'],
                    'email' => $validated['email'],
                ];

                // Only update password if provided
                if (!empty($validated['password'])) {
                    $userData['password'] = bcrypt($validated['password']);
                }

                $user->update($userData);

                // Update or create teacher record
                $teacherData = [
                    'street_address' => $validated['street_address'] ?? null,
                    'barangay' => $validated['barangay'] ?? null,
                    'municipality' => $validated['municipality'] ?? null,
                    'province' => $validated['province'] ?? null,
                    'phone' => $validated['phone'],
                    'birthdate' => $validated['birthdate'],
                    'gender' => $validated['gender'],
                    'status' => $validated['status'],
                ];

                \Log::info('Teacher data to update:', $teacherData);

                if ($user->teacher) {
                    Teacher::where('user_id', $user->id)->update($teacherData);
                } else {
                    $teacherData['user_id'] = $user->id;
                    $teacherData['employee_id'] = $user->username;
                    Teacher::create($teacherData);
                }

                DB::commit();
                return redirect()->route('admin.teachers.index')->with('success', 'Teacher updated successfully!');
            } catch (\Exception $e) {
                DB::rollBack();
                \Log::error('Database update failed:', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'user_data' => $userData ?? null,
                    'teacher_data' => $teacherData ?? null
                ]);
                throw $e;
            }
        } catch (\Exception $e) {
            \Log::error('Error updating teacher:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to update teacher. Please check the information and try again.');
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            
            $user = User::findOrFail($id);

            // Check if teacher has any assigned subjects
            if ($user->subjects()->exists()) {
                // Soft delete all subjects
                $user->subjects()->delete();
            }

            // Delete related records
            if ($user->teacher) {
                // Delete event views
                EventView::where('user_id', $user->id)->delete();
                
                // Remove teacher from section if they are an adviser
                DB::table('sections')
                    ->where('adviser_id', $user->id)
                    ->update(['adviser_id' => null]);
                
                // Delete the teacher record using user_id
                Teacher::where('user_id', $user->id)->forceDelete();
            }

            // Delete the user record
            $user->forceDelete();

            DB::commit();
            return redirect()->route('admin.teachers.index')
                ->with('success', 'Teacher deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error deleting teacher: ' . $e->getMessage());
            return redirect()->route('admin.teachers.index')
                ->with('error', 'Failed to delete teacher: ' . $e->getMessage());
        }
    }

    public function show($id)
    {
        $teacher = User::with('teacher')->findOrFail($id);
        return view('admin.teachers.show', compact('teacher'));
    }

    /**
     * Download the CSV template for teacher import
     */
    public function downloadTemplate()
    {
        $csv = Writer::createFromString('');
        
        // Add headers
        $csv->insertOne([
            'last_name',
            'first_name',
            'middle_name',
            'suffix',
            'email',
            'street_address',
            'barangay',
            'municipality',
            'province',
            'phone',
            'birthdate',
            'gender'
        ]);

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="teacher_import_template.csv"',
        ];

        return response($csv->toString(), 200, $headers);
    }

    /**
     * Import teachers from CSV file
     */
    public function importTeachers(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240' // max 10MB
        ]);

        try {
            DB::beginTransaction();

            $csv = Reader::createFromPath($request->file('csv_file')->getPathname());
            $csv->setHeaderOffset(0);

            // Validate required headers
            $requiredHeaders = [
                'last_name',
                'first_name',
                'email',
                'gender'
            ];

            $headers = $csv->getHeader();
            $missingHeaders = array_diff($requiredHeaders, $headers);

            if (!empty($missingHeaders)) {
                return redirect()->route('admin.teachers.index')
                    ->with('error', 'Invalid CSV format. Missing required columns: ' . implode(', ', $missingHeaders) . '. Please download the template and follow the correct format.');
            }

            $records = $csv->getRecords();
            $importedCount = 0;
            $updatedCount = 0;
            $errors = [];
            $rowNumber = 2; // Start from 2 since row 1 is header

            foreach ($records as $record) {
                try {
                    // Validate required fields
                    if (empty($record['last_name']) || empty($record['first_name']) || empty($record['email']) || 
                        empty($record['gender'])) {
                        throw new \Exception('Required fields missing');
                    }

                    // Validate email format
                    if (!filter_var($record['email'], FILTER_VALIDATE_EMAIL)) {
                        throw new \Exception('Invalid email format');
                    }

                    // Validate gender
                    if (!in_array($record['gender'], ['Male', 'Female', 'Other'])) {
                        throw new \Exception('Invalid gender value. Must be Male, Female, or Other');
                    }

                    // Validate date format for birthdate if provided
                    if (!empty($record['birthdate'])) {
                        $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                        if (!$date || $date->format('m/d/Y') !== $record['birthdate']) {
                            throw new \Exception("Invalid birthdate format. Must be MM/DD/YYYY");
                        }
                    }

                    // Check if email already exists
                    $existingUser = User::where('email', $record['email'])->first();
                    
                    if ($existingUser) {
                        // Update existing teacher
                        $user = $existingUser;
                        
                        // Update user record
                        $user->last_name = $record['last_name'];
                        $user->first_name = $record['first_name'];
                        $user->middle_name = $record['middle_name'] ?? $user->middle_name;
                        $user->suffix = $record['suffix'] ?? $user->suffix;
                        $user->email = $record['email'];
                        $user->save();

                        // Convert date format from MM/DD/YYYY to YYYY-MM-DD if birthdate is provided
                        $birthdate = null;
                        if (!empty($record['birthdate'])) {
                            $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                            $birthdate = $date->format('Y-m-d');
                        }

                        // Update teacher record
                        if ($user->teacher) {
                            $user->teacher->update([
                                'street_address' => $record['street_address'] ?? $user->teacher->street_address,
                                'barangay' => $record['barangay'] ?? $user->teacher->barangay,
                                'municipality' => $record['municipality'] ?? $user->teacher->municipality,
                                'province' => $record['province'] ?? $user->teacher->province,
                                'birthdate' => $birthdate ?? $user->teacher->birthdate,
                                'phone' => $record['phone'] ?? $user->teacher->phone,
                                'gender' => $record['gender'],
                                'status' => 'active'
                            ]);
                        } else {
                            // Create teacher record if it doesn't exist
                            $teacher = new Teacher();
                            $teacher->user_id = $user->id;
                            $teacher->employee_id = $user->username;
                            $teacher->street_address = $record['street_address'] ?? null;
                            $teacher->barangay = $record['barangay'] ?? null;
                            $teacher->municipality = $record['municipality'] ?? null;
                            $teacher->province = $record['province'] ?? null;
                            $teacher->birthdate = $birthdate;
                            $teacher->phone = $record['phone'] ?? null;
                            $teacher->gender = $record['gender'];
                            $teacher->status = 'active';
                            $teacher->save();
                        }

                        $updatedCount++;
                    } else {
                        // Get the latest teacher ID and generate the next one
                        $latestTeacher = Teacher::orderBy('user_id', 'desc')->first();
                        $nextId = $latestTeacher ? intval(substr($latestTeacher->employee_id, 3)) + 1 : 1;
                        $employeeId = 'TCH' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

                        // Generate temporary password
                        $tempPassword = 'TEMP' . str_pad($nextId, 5, '0', STR_PAD_LEFT);

                        // Create user record
                        $user = new User();
                        $user->last_name = $record['last_name'];
                        $user->first_name = $record['first_name'];
                        $user->middle_name = $record['middle_name'] ?? null;
                        $user->suffix = $record['suffix'] ?? null;
                        $user->email = $record['email'];
                        $user->username = $employeeId;
                        $user->password = bcrypt($tempPassword);
                        $user->role = 'teacher';
                        $user->save();

                        // Convert date format
                        $birthdate = null;
                        if (!empty($record['birthdate'])) {
                            $date = \DateTime::createFromFormat('m/d/Y', $record['birthdate']);
                            $birthdate = $date->format('Y-m-d');
                        }

                        // Create teacher record
                        $teacher = new Teacher();
                        $teacher->user_id = $user->id;
                        $teacher->employee_id = $employeeId;
                        $teacher->street_address = $record['street_address'] ?? null;
                        $teacher->barangay = $record['barangay'] ?? null;
                        $teacher->municipality = $record['municipality'] ?? null;
                        $teacher->province = $record['province'] ?? null;
                        $teacher->birthdate = $birthdate;
                        $teacher->phone = $record['phone'] ?? null;
                        $teacher->gender = $record['gender'];
                        $teacher->status = 'active';
                        $teacher->save();

                        $importedCount++;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Row {$rowNumber}: " . $e->getMessage();
                }
                $rowNumber++;
            }

            if (count($errors) > 0) {
                DB::rollBack();
                return redirect()->route('admin.teachers.index')
                    ->with('error', 'Import failed with the following errors:<br>' . implode('<br>', $errors));
            }

            DB::commit();

            $message = [];
            if ($importedCount > 0) {
                $message[] = "Imported {$importedCount} new teachers";
            }
            if ($updatedCount > 0) {
                $message[] = "Updated {$updatedCount} existing teachers";
            }

            return redirect()->route('admin.teachers.index')
                ->with('success', implode('. ', $message));

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('admin.teachers.index')
                ->with('error', 'Failed to import teachers: ' . $e->getMessage());
        }
    }
}