<?php $__env->startSection('content'); ?>
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="row mb-4">
            <!-- Title Section -->
            <div class="col-12 mb-3">
                <div>
                    <h2 class="fw-bold mb-1 text-primary">Student Management</h2>
                    <p class="text-muted mb-0 small">Manage and monitor student records and information</p>
                </div>
            </div>

            <!-- Search, Filter and Action Buttons Section -->
            <div class="col-12">
                <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap">
                    <!-- Left Side: Search and Filter -->
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <!-- Enhanced Search Container -->
                        <div class="search-container position-relative" style="min-width: 300px;">
                            <div class="search-input-wrapper">
                                <i class="bi bi-search search-icon"></i>
                                <input type="text" name="search" class="search-input" placeholder="Search students by name, LRN, or section..."
                                    value="<?php echo e(request('search')); ?>">
                                <div class="search-loading-spinner" style="display: none;">
                                    <div class="spinner"></div>
                                </div>
                                <button type="button" class="search-clear-btn" style="display: none;">
                                    <i class="bi bi-x-circle-fill"></i>
                                </button>
                            </div>
                            <div class="search-suggestions" style="display: none;">
                                <!-- Dynamic search suggestions will appear here -->
                            </div>
                        </div>

                        <!-- Filter Dropdown -->
                        <div class="dropdown position-relative">
                            <button class="btn btn-secondary btn-action dropdown-toggle <?php echo e(request('grade_level') ? 'filter-active' : ''); ?>" type="button" id="studentFilterDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true">
                                <i class="bi bi-funnel me-2"></i>
                                <?php echo e(request('grade_level') ? 'Grade ' . request('grade_level') : 'All Students'); ?>

                            </button>
                            <ul class="dropdown-menu" aria-labelledby="studentFilterDropdown">
                                <li>
                                    <a class="dropdown-item <?php echo e(!request('grade_level') ? 'active' : ''); ?>"
                                        href="<?php echo e(route('admin.students.index', ['search' => request('search')])); ?>">
                                        <i class="bi bi-grid-3x3-gap me-2"></i> All Students
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <?php for($i = 7; $i <= 12; $i++): ?>
                                    <li>
                                        <a class="dropdown-item <?php echo e(request('grade_level') == $i ? 'active' : ''); ?>"
                                            href="<?php echo e(route('admin.students.index', ['grade_level' => $i, 'search' => request('search')])); ?>">
                                            <i class="bi bi-mortarboard me-2"></i> Grade <?php echo e($i); ?>

                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Right Side: Action Buttons -->
                    <div class="d-flex gap-2 flex-wrap">
                        <!-- Bulk Assignment Button (Hidden by default) -->
                        <button type="button" class="btn btn-warning btn-action" id="bulkAssignBtn"
                                style="display: none;" data-bs-toggle="modal" data-bs-target="#bulkAssignModal">
                            <i class="bi bi-mortarboard me-2"></i>
                            Assign Grade & Section (<span id="assignSelectedCount">0</span>)
                        </button>
                        <!-- Bulk Delete Button (Hidden by default) -->
                        <button type="button" class="btn btn-danger btn-action" id="bulkDeleteBtn"
                                style="display: none;" data-bs-toggle="modal" data-bs-target="#bulkDeleteModal">
                            <i class="bi bi-trash me-2"></i>
                            Delete Selected (<span id="selectedCount">0</span>)
                        </button>
                        <button type="button" class="btn btn-success btn-action" data-bs-toggle="modal"
                            data-bs-target="#uploadCsvModal">
                            <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                            Import CSV
                        </button>
                        <a href="<?php echo e(route('admin.students.create')); ?>" class="btn btn-primary btn-action">
                            <i class="bi bi-plus-lg me-2"></i>
                            Add Student
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                <!-- Toast notifications will appear in bottom right corner -->

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary" style="width: 50px;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                        <label class="form-check-label" for="selectAll"></label>
                                    </div>
                                </th>
                                <th class="text-primary">Student ID</th>
                                <th class="text-primary">Name</th>
                                <th class="text-primary">Email</th>
                                <th class="text-primary">Status</th>
                                <th class="text-primary text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $students; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <tr>
                                                    <td>
                                                        <div class="form-check">
                                                            <input class="form-check-input student-checkbox" type="checkbox"
                                                                   value="<?php echo e($student->id); ?>" id="student_<?php echo e($student->id); ?>">
                                                            <label class="form-check-label" for="student_<?php echo e($student->id); ?>"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                                <i class="bi bi-person-badge text-primary"></i>
                                                            </div>
                                                            <span class="fw-medium"><?php echo e($student->student?->student_id ?? 'N/A'); ?></span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                                <i class="bi bi-person text-info"></i>
                                                            </div>
                                                            <div>
                                                                <div class="fw-medium"><?php echo e($student->formal_name); ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle me-2">
                                                                <i class="bi bi-envelope text-warning"></i>
                                                            </div>
                                                            <a href="mailto:<?php echo e($student->email); ?>"
                                                                class="text-decoration-none"><?php echo e($student->email); ?></a>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo e($student->student?->status === 'active' ? 'success' :
                                ($student->student?->status === 'inactive' ? 'warning' :
                                    ($student->student?->status === 'dropped' ? 'danger' :
                                        ($student->student?->status === 'graduated' ? 'info' :
                                            ($student->student?->status === 'transferred' ? 'secondary' : 'secondary'))))); ?>">
                                                            <i class="bi bi-<?php echo e($student->student?->status === 'active' ? 'check-circle' :
                                ($student->student?->status === 'inactive' ? 'pause-circle' :
                                    ($student->student?->status === 'dropped' ? 'x-circle' :
                                        ($student->student?->status === 'graduated' ? 'mortarboard' :
                                            ($student->student?->status === 'transferred' ? 'arrow-right-circle' : 'question-circle'))))); ?> me-1"></i>
                                                            <?php echo e(ucfirst($student->student?->status ?? 'inactive')); ?>

                                                        </span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex justify-content-end">
                                                            <!-- Action Dropdown -->
                                                            <div class="dropdown">
                                                                <button class="btn btn-sm btn-outline-secondary btn-table-action dropdown-toggle" type="button"
                                                                        id="actionDropdown<?php echo e($student->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-gear me-1"></i>
                                                                    Select
                                                                </button>
                                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="actionDropdown<?php echo e($student->id); ?>">
                                                                    <li>
                                                                        <a class="dropdown-item" href="<?php echo e(route('admin.students.show', $student->id)); ?>">
                                                                            <i class="bi bi-eye me-2"></i>
                                                                            View Details
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a class="dropdown-item" href="<?php echo e(route('admin.students.edit', $student->id)); ?>">
                                                                            <i class="bi bi-pencil me-2"></i>
                                                                            Edit Student
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-people fs-2 d-block mb-2"></i>
                                            No students found
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Section -->
                <div class="row mt-4 pt-3 border-top">
                    <div class="col-md-6 col-12 mb-2 mb-md-0">
                        <div class="text-muted small d-flex align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            Showing <?php echo e($students->firstItem() ?? 0); ?> to <?php echo e($students->lastItem() ?? 0); ?> of
                            <?php echo e($students->total() ?? 0); ?> entries
                        </div>
                    </div>
                    <div class="col-md-6 col-12">
                        <div class="d-flex justify-content-md-end justify-content-center align-items-center gap-2">
                            <?php if($students->hasPages()): ?>
                                <?php if($students->onFirstPage()): ?>
                                    <button class="btn btn-outline-secondary btn-action pagination-btn" disabled>
                                        <i class="bi bi-chevron-left me-2"></i>
                                        Previous
                                    </button>
                                <?php else: ?>
                                    <a href="<?php echo e($students->previousPageUrl()); ?>" class="btn btn-outline-secondary btn-action pagination-btn">
                                        <i class="bi bi-chevron-left me-2"></i>
                                        Previous
                                    </a>
                                <?php endif; ?>

                                <!-- Page Info -->
                                <span class="px-3 py-2 text-muted small">
                                    Page <?php echo e($students->currentPage()); ?> of <?php echo e($students->lastPage()); ?>

                                </span>

                                <?php if($students->hasMorePages()): ?>
                                    <a href="<?php echo e($students->nextPageUrl()); ?>" class="btn btn-primary btn-action pagination-btn">
                                        Next
                                        <i class="bi bi-chevron-right ms-2"></i>
                                    </a>
                                <?php else: ?>
                                    <button class="btn btn-outline-secondary btn-action pagination-btn" disabled>
                                        Next
                                        <i class="bi bi-chevron-right ms-2"></i>
                                    </button>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container for Bottom Right Notifications -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1055;">
        <!-- Toast notifications will be dynamically added here -->
    </div>

    <!-- Bulk Delete Confirmation Modal -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="bulkDeleteModalLabel">Confirm Bulk Deletion</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="avatar-sm bg-danger bg-opacity-10 rounded-circle mx-auto mb-3">
                            <i class="bi bi-exclamation-triangle-fill text-danger fs-4"></i>
                        </div>
                        <h5 class="mb-1">Are you sure?</h5>
                        <p class="text-muted mb-0">You are about to delete <span class="fw-bold" id="bulkDeleteCount"></span> selected student(s).
                            This action cannot be undone.</p>
                    </div>
                </div>
                <div class="modal-footer border-0 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-outline-secondary btn-action" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-danger btn-action" id="confirmBulkDeleteBtn">
                        <i class="bi bi-trash me-2"></i>
                        Delete Selected Students
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Assignment Modal -->
    <div class="modal fade" id="bulkAssignModal" tabindex="-1" aria-labelledby="bulkAssignModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="bulkAssignModalLabel">Assign Grade Level & Section</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo e(route('admin.students.bulk-assign')); ?>" method="POST" id="bulkAssignForm">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle mx-auto mb-3">
                                <i class="bi bi-mortarboard text-warning fs-4"></i>
                            </div>
                            <h5 class="mb-1">Assign Students</h5>
                            <p class="text-muted mb-0">You are about to assign grade level and section to <span class="fw-bold" id="bulkAssignCount"></span> selected student(s).</p>
                        </div>

                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label fw-medium">Grade Level <span class="text-danger">*</span></label>
                                <select name="grade_level" id="bulkGradeLevel" class="form-select" required>
                                    <option value="">Select Grade Level</option>
                                    <?php for($i = 7; $i <= 12; $i++): ?>
                                        <option value="Grade <?php echo e($i); ?>">Grade <?php echo e($i); ?></option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                            <div class="col-12" id="sectionListContainer" style="display: none;">
                                <label class="form-label fw-medium">Select Section <span class="text-danger">*</span></label>
                                <div class="section-list-wrapper">
                                    <div class="alert alert-info small mb-3">
                                        <i class="bi bi-info-circle me-2"></i>
                                        Choose a section from the list below. Only sections for the selected grade level are shown.
                                    </div>
                                    <div id="sectionsList" class="sections-grid">
                                        <!-- Sections will be populated here -->
                                    </div>
                                </div>
                                <input type="hidden" name="section_id" id="selectedSectionId" required>
                            </div>
                        </div>

                        <!-- Hidden input to store selected student IDs -->
                        <input type="hidden" name="student_ids" id="bulkAssignStudentIds">
                    </div>
                    <div class="modal-footer border-0 d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-action" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-warning btn-action">
                            <i class="bi bi-mortarboard me-2"></i>
                            Assign Students
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- CSV Upload Modal -->
    <div class="modal fade" id="uploadCsvModal" tabindex="-1" aria-labelledby="uploadCsvModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadCsvModalLabel">Import Students from CSV</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo e(route('admin.students.import')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="csvFile" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                            <div class="form-text">
                                The CSV file should have the following columns:
                                <ul class="mb-0 mt-1">
                                    <li><strong>Required:</strong> last_name, first_name, email, lrn, gender, birthdate</li>
                                    <li><strong>Optional:</strong> middle_name, suffix, street_address, barangay,
                                        municipality, province, phone</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong>
                            <ul class="mb-0 mt-1">
                                <li>Make sure your CSV file follows the required format</li>
                                <li>Dates must be in MM/DD/YYYY format</li>
                                <li>Gender must be one of: Male, Female, Other</li>
                                <li>Email addresses must be valid</li>
                            </ul>
                        </div>
                        <a href="<?php echo e(route('admin.students.template')); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-download me-2"></i>
                            Download Template
                        </a>
                    </div>
                    <div class="modal-footer border-0 d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-action" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-success btn-action">
                            <i class="bi bi-upload me-2"></i>
                            Import Students
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Action Buttons Container */
        .action-buttons-container {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* Button Styling - Smaller size to match filter dropdown */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            box-sizing: border-box;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-success.btn-action {
            background: linear-gradient(135deg, #10b981, #34d399);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-success.btn-action:hover {
            background: linear-gradient(135deg, #059669, #10b981);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-danger.btn-action {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-danger.btn-action:hover {
            background: linear-gradient(135deg, #b91c1c, #dc2626);
            box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-warning.btn-action {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
            color: white;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .btn-warning.btn-action:hover {
            background: linear-gradient(135deg, #d97706, #f59e0b);
            box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        /* Table Action Buttons */
        .btn-table-action {
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            transition: var(--transition);
            min-width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-table-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        /* Action Dropdown Styling */
        .btn-table-action.dropdown-toggle {
            min-width: 80px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid #e5e7eb;
            background: #fff;
            color: #6b7280;
            transition: all 0.2s ease;
        }

        .btn-table-action.dropdown-toggle:hover {
            border-color: #d1d5db;
            background: #f9fafb;
            color: #374151;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-table-action.dropdown-toggle:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .btn-table-action.dropdown-toggle::after {
            margin-left: 6px;
            font-size: 10px;
        }

        .btn-table-action.dropdown-toggle[aria-expanded="true"] {
            border-color: #6366f1;
            background: #f8faff;
            color: #4f46e5;
        }

        /* Dropdown Container Stacking */
        .dropdown {
            position: relative;
            z-index: 1000;
        }

        .dropdown.show {
            z-index: 9998;
        }

        /* Dropdown Menu Styling */
        .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            padding: 8px;
            margin-top: 4px;
            min-width: 160px;
            max-width: 200px;
            z-index: 999999 !important;
            position: absolute !important;
            transform: translateZ(0);
            will-change: transform;
            isolation: isolate;
        }

        .dropdown-menu.show {
            z-index: 999999 !important;
            display: block !important;
            position: absolute !important;
        }

        .dropdown-item {
            padding: 10px 12px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            white-space: nowrap;
            overflow: visible;
            text-overflow: clip;
            height: auto;
            line-height: 1.4;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: #4f46e5;
            transform: translateX(2px);
        }

        .dropdown-item i {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        /* Table and Row Stacking Context */
        .table-responsive {
            position: relative;
            z-index: 1;
        }

        .table tbody tr {
            position: relative;
            z-index: 2;
        }

        .table tbody tr:hover {
            z-index: 3;
        }

        .table tbody tr.dropdown-active {
            z-index: 10000 !important;
            position: relative;
        }

        /* Hide scrollbars from dropdown menus */
        .dropdown-menu::-webkit-scrollbar {
            display: none; /* WebKit browsers */
        }

        .dropdown-menu {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        /* Ensure dropdown appears above table elements */
        .table tbody tr td .dropdown {
            position: relative;
            z-index: 1000;
        }

        .table tbody tr td .dropdown-menu {
            position: absolute !important;
            z-index: 10000 !important;
            top: 100% !important;
            right: 0 !important;
            left: auto !important;
            transform: none !important;
        }

        .pagination-btn {
            min-width: 120px;
        }

        .pagination-btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
        }

        .card.shadow-sm {
            background-color: #f8f9fa;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-sm i {
            font-size: 1rem;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
        }

        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
        }

        .alert-success {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        /* Toast Notification Styles */
        .toast {
            min-width: 350px;
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.85);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .toast::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            border-radius: 16px 16px 0 0;
        }

        .toast.success {
            background: rgba(240, 253, 244, 0.85);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .toast.success::before {
            background: linear-gradient(90deg, #10b981, #34d399);
        }

        .toast.error {
            background: rgba(254, 242, 242, 0.85);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .toast.error::before {
            background: linear-gradient(90deg, #ef4444, #f87171);
        }

        .toast.warning {
            background: rgba(255, 251, 235, 0.85);
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .toast.warning::before {
            background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }

        /* Checkbox Styling */
        .form-check-input {
            width: 1.2em;
            height: 1.2em;
            border: 2px solid #cbd5e0;
            border-radius: 4px;
            transition: var(--transition);
        }

        .form-check-input:checked {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        .form-check-input:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
        }

        .form-check-input:indeterminate {
            background-color: var(--primary);
            border-color: var(--primary);
        }

        /* Bulk Delete Button Animation */
        #bulkDeleteBtn {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Section List Styling */
        .section-list-wrapper {
            max-height: 300px;
            overflow-y: auto;
        }

        .sections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 12px;
        }

        .section-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fff;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .section-card:hover {
            border-color: #6366f1;
            background: #f8faff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }

        .section-card.selected {
            border-color: #6366f1;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
        }

        .section-card.selected:hover {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
        }

        .section-card .section-name {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 4px;
        }

        .section-card .section-info {
            font-size: 12px;
            opacity: 0.8;
        }

        .section-card .section-students {
            font-size: 11px;
            margin-top: 8px;
            padding: 4px 8px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            display: inline-block;
        }

        .section-card:not(.selected) .section-students {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }

        /* Responsive sections grid */
        @media (max-width: 768px) {
            .sections-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
                gap: 8px;
            }

            .section-card {
                padding: 12px;
            }

            .section-card .section-name {
                font-size: 14px;
            }
        }

        .toast-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.15);
            padding: 14px 18px;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border-radius: 16px 16px 0 0;
        }

        .toast.success .toast-header {
            background: rgba(16, 185, 129, 0.1);
            border-bottom: 1px solid rgba(16, 185, 129, 0.15);
        }

        .toast.error .toast-header {
            background: rgba(239, 68, 68, 0.1);
            border-bottom: 1px solid rgba(239, 68, 68, 0.15);
        }

        .toast.warning .toast-header {
            background: rgba(245, 158, 11, 0.1);
            border-bottom: 1px solid rgba(245, 158, 11, 0.15);
        }

        .toast-body {
            padding: 14px 18px;
            font-size: 14px;
            line-height: 1.6;
            color: rgba(31, 41, 55, 0.9);
            font-weight: 500;
        }

        .toast .btn-close {
            margin: 0;
            padding: 8px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .toast .btn-close:hover {
            background: rgba(255, 255, 255, 0.3);
            opacity: 1;
            transform: scale(1.1);
        }

        .toast-icon {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 12px;
            color: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .toast-icon::before {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            z-index: -1;
        }

        .toast-icon.success {
            background: linear-gradient(135deg, #10b981, #34d399);
        }

        .toast-icon.error {
            background: linear-gradient(135deg, #ef4444, #f87171);
        }

        .toast-icon.warning {
            background: linear-gradient(135deg, #f59e0b, #fbbf24);
        }

        /* Toast Animation */
        .toast.showing {
            animation: slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .toast.hide {
            animation: slideOutRight 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19);
        }

        .toast:hover {
            transform: translateY(-2px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        @keyframes slideInRight {
            0% {
                transform: translateX(100%) scale(0.9);
                opacity: 0;
            }
            50% {
                transform: translateX(-10px) scale(1.02);
                opacity: 0.8;
            }
            100% {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            0% {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
            100% {
                transform: translateX(100%) scale(0.9);
                opacity: 0;
            }
        }

        /* Subtle glow effect for different toast types */
        .toast.success {
            box-shadow: 0 12px 40px rgba(16, 185, 129, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.1);
        }

        .toast.error {
            box-shadow: 0 12px 40px rgba(239, 68, 68, 0.15), 0 0 0 1px rgba(239, 68, 68, 0.1);
        }

        .toast.warning {
            box-shadow: 0 12px 40px rgba(245, 158, 11, 0.15), 0 0 0 1px rgba(245, 158, 11, 0.1);
        }

        /* Toast Container Enhancement */
        .toast-container {
            transition: all 0.3s ease;
        }

        .toast-container.new-toast {
            animation: containerPulse 0.6s ease-out;
        }

        @keyframes containerPulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.02);
            }
            100% {
                transform: scale(1);
            }
        }

        /* Enhanced backdrop blur for better transparency effect */
        .toast {
            backdrop-filter: blur(25px) saturate(180%);
            -webkit-backdrop-filter: blur(25px) saturate(180%);
        }

        /* Dropdown Container Stability */
        .dropdown {
            position: relative !important;
            z-index: 1;
            display: inline-block;
        }

        .dropdown-toggle {
            position: relative;
            z-index: 2;
        }

        /* Force dropdown positioning - Align with button */
        .dropdown.show #studentFilterDropdown + .dropdown-menu {
            display: block !important;
            position: absolute !important;
            top: calc(100% + 8px) !important;
            left: 0 !important;
            transform: translateX(0) !important;
            min-width: 200px;
        }

        /* Enhanced Filter Dropdown Styling - Only for filter dropdowns */
        #studentFilterDropdown + .dropdown-menu {
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            padding: 12px;
            margin-top: 8px !important;
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.98);
            min-width: 200px;
            width: max-content;
            transform-origin: top left;
            transition: opacity 0.15s ease, transform 0.15s ease;
            position: absolute !important;
            top: calc(100% + 4px) !important;
            left: 0 !important;
            right: auto !important;
            bottom: auto !important;
            z-index: 1050;
            will-change: transform, opacity;
            transform: translateX(0) !important;
        }

        /* Filter Dropdown Arrow Pointer - Aligned to right side of button */
        #studentFilterDropdown + .dropdown-menu::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 114px; /* Positioned to align with the right side of the filter button (~130px button width - 16px offset) */
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.98);
            transform: rotate(45deg);
            border-radius: 2px;
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        #studentFilterDropdown + .dropdown-menu.show {
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .dropdown-item {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 4px;
            transition: var(--transition);
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .dropdown-item:last-child {
            margin-bottom: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: var(--primary-dark);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }

        .dropdown-item.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            transform: translateX(1px);
        }

        .dropdown-item.active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .dropdown-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            transition: var(--transition);
        }

        .dropdown-item:hover i {
            transform: scale(1.05);
        }

        .dropdown-item.active i {
            color: white;
        }

        .dropdown-divider {
            margin: 8px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            opacity: 1;
        }

        /* Enhanced Dropdown Toggle Button */
        .dropdown-toggle::after {
            margin-left: 8px;
            transition: var(--transition);
        }

        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .dropdown-toggle:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        /* Dropdown Button Enhancements */
        .btn-secondary.btn-action.dropdown-toggle {
            position: relative;
            overflow: hidden;
        }

        .btn-secondary.btn-action.dropdown-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-secondary.btn-action.dropdown-toggle:hover::before {
            left: 100%;
        }

        /* Filter Active State */
        .btn-secondary.btn-action.dropdown-toggle.filter-active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            color: white;
            border-color: var(--primary-dark);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active i {
            color: white;
        }



        /* Enhanced Search Container */
        .search-container {
            position: relative;
            width: 320px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 6px;
            height: 48px;
            box-sizing: border-box;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .search-input-wrapper:hover {
            border-color: #cbd5e0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-input-wrapper:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-icon {
            color: #64748b;
            font-size: 17px;
            margin-left: 14px;
            margin-right: 10px;
            transition: var(--transition);
            z-index: 2;
        }

        .search-input-wrapper:focus-within .search-icon {
            color: var(--primary);
            transform: scale(1.1);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 12px 10px;
            font-size: 15px;
            font-weight: 500;
            color: #1e293b;
            placeholder-color: #94a3b8;
            height: 100%;
        }

        .search-input::placeholder {
            color: #94a3b8;
            font-weight: 400;
        }

        .search-loading-spinner {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #64748b;
            font-size: 16px;
            margin-right: 8px;
            padding: 4px;
            border-radius: 50%;
            transition: var(--transition);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn:hover {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

        /* Search Suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            z-index: 1000;
            margin-top: 4px;
            max-height: 300px;
            overflow-y: auto;
            backdrop-filter: blur(8px);
        }

        .search-suggestion-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-suggestion-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(129, 140, 248, 0.02));
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .search-suggestion-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .search-suggestion-content {
            flex: 1;
        }

        .search-suggestion-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .search-suggestion-subtitle {
            color: #64748b;
            font-size: 12px;
        }



        /* Search Status Indicator */
        .search-status {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .action-buttons-container {
                gap: 8px;
                justify-content: flex-start;
            }

            .btn-action {
                padding: 8px 12px;
                font-size: 13px;
                min-width: 110px;
                height: 38px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .btn-table-action {
                padding: 6px 8px;
                min-width: 32px;
                height: 32px;
                font-size: 11px;
            }

            .btn-table-action.dropdown-toggle {
                min-width: 70px;
                padding: 6px 8px;
                font-size: 11px;
            }

            .dropdown-menu {
                min-width: 140px;
                padding: 6px;
            }

            .dropdown-item {
                padding: 8px 10px;
                font-size: 12px;
            }

            .pagination-btn {
                min-width: 100px;
                padding: 8px 12px;
                font-size: 12px;
            }

            /* Toast Mobile Responsive */
            .toast {
                min-width: 300px;
                max-width: 90vw;
                margin-bottom: 10px;
                border-radius: 14px;
            }

            .toast-container {
                padding: 16px !important;
            }

            .toast-header {
                padding: 12px 14px;
                font-size: 13px;
                border-radius: 14px 14px 0 0;
            }

            .toast-body {
                padding: 12px 14px;
                font-size: 13px;
                line-height: 1.5;
            }

            .toast-icon {
                width: 20px;
                height: 20px;
                font-size: 11px;
                margin-right: 8px;
            }

            .toast .btn-close {
                padding: 6px;
                font-size: 11px;
            }

            .toast:hover {
                transform: translateY(-1px);
                box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
            }
        }

            /* Enhanced Search Mobile Responsive */
            .search-container {
                width: 100%;
                max-width: 280px;
            }

            .search-input-wrapper {
                border-radius: 12px;
                padding: 4px;
                height: 44px;
                box-sizing: border-box;
            }

            .search-input {
                padding: 10px 8px;
                font-size: 14px;
                height: 100%;
            }

            .search-input::placeholder {
                font-size: 13px;
            }

            .search-icon {
                font-size: 15px;
                margin-left: 12px;
                margin-right: 8px;
            }

            .search-clear-btn {
                font-size: 14px;
                margin-right: 6px;
            }

            .search-suggestions {
                border-radius: 10px;
                max-height: 250px;
            }

            .search-suggestion-item {
                padding: 10px 12px;
            }

            .search-suggestion-icon {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .search-suggestion-title {
                font-size: 13px;
            }

            .search-suggestion-subtitle {
                font-size: 11px;
            }
        }

        @media (max-width: 576px) {
            .action-buttons-container {
                gap: 6px;
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: 240px;
            }

            .search-input::placeholder {
                content: "Search students...";
            }

            /* Enhanced Filter Dropdown Mobile Responsive */
            #studentFilterDropdown + .dropdown-menu {
                border-radius: 12px;
                padding: 8px;
                min-width: 180px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }

            .dropdown-item {
                padding: 10px 12px;
                border-radius: 8px;
                font-size: 13px;
            }

            .dropdown-item i {
                width: 18px;
                height: 18px;
                font-size: 12px;
                margin-right: 10px;
            }

            .dropdown-toggle::after {
                margin-left: 6px;
            }

            /* Enhanced Filter Dropdown Mobile Responsive Arrow */
            #studentFilterDropdown + .dropdown-menu::before {
                left: 94px; /* Adjusted for mobile to align with right side of smaller button (~110px button width - 16px offset) */
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Toast Notification System
            function showToast(message, type = 'success', duration = 5000) {
                const toastContainer = document.querySelector('.toast-container');
                const toastId = 'toast-' + Date.now();

                const iconClass = type === 'success' ? 'bi-check-circle-fill' :
                                 type === 'error' ? 'bi-exclamation-circle-fill' :
                                 'bi-info-circle-fill';

                const toastHtml = `
                    <div id="${toastId}" class="toast ${type} showing" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="toast-header">
                            <div class="toast-icon ${type}">
                                <i class="${iconClass}"></i>
                            </div>
                            <strong class="me-auto">${type === 'success' ? 'Success' : type === 'error' ? 'Error' : 'Info'}</strong>
                            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                `;

                toastContainer.insertAdjacentHTML('beforeend', toastHtml);

                // Add pulse effect to container
                toastContainer.classList.add('new-toast');
                setTimeout(() => {
                    toastContainer.classList.remove('new-toast');
                }, 600);

                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement, {
                    autohide: true,
                    delay: duration
                });

                // Show the toast
                toast.show();

                // Remove the toast element after it's hidden
                toastElement.addEventListener('hidden.bs.toast', function() {
                    toastElement.remove();
                });

                return toast;
            }

            // Check for session messages and show toasts
            <?php if(session('success')): ?>
                showToast('<?php echo e(session('success')); ?>', 'success');
            <?php endif; ?>

            <?php if(session('error')): ?>
                showToast('<?php echo e(session('error')); ?>', 'error');
            <?php endif; ?>

            <?php if(session('warning')): ?>
                showToast('<?php echo e(session('warning')); ?>', 'warning');
            <?php endif; ?>

            // Make showToast globally available for other scripts
            window.showToast = showToast;

            const searchInput = document.querySelector('.search-input');
            const searchContainer = document.querySelector('.search-container');
            const loadingSpinner = document.querySelector('.search-loading-spinner');
            const clearBtn = document.querySelector('.search-clear-btn');
            const searchSuggestions = document.querySelector('.search-suggestions');

            const searchTimeout = 150;
            let timeoutId;
            let currentRequest = null;

            // Show/hide clear button based on input value
            function toggleClearButton() {
                if (searchInput.value.trim()) {
                    clearBtn.style.display = 'flex';
                } else {
                    clearBtn.style.display = 'none';
                }
            }

            // Show loading spinner
            function showLoading() {
                loadingSpinner.style.display = 'flex';
                clearBtn.style.display = 'none';
            }

            // Hide loading spinner
            function hideLoading() {
                loadingSpinner.style.display = 'none';
                toggleClearButton();
            }

            // Clear search
            clearBtn.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.focus();
                toggleClearButton();

                // Clear search and show all rows
                performRealTimeSearch();
            });

            // Handle input events - Real-time search
            searchInput.addEventListener('input', function () {
                clearTimeout(timeoutId);
                toggleClearButton();

                const searchValue = this.value.trim();

                // Show loading for longer searches
                if (searchValue.length > 2) {
                    showLoading();
                }

                // Perform real-time search with slight delay for better UX
                timeoutId = setTimeout(() => {
                    hideLoading();
                    performRealTimeSearch();
                }, 100); // Much faster response time
            });

            // Real-time search function - shows/hides rows without changing content
            function performRealTimeSearch() {
                const searchTerm = searchInput.value.trim().toLowerCase();
                const tableBody = document.querySelector('tbody');
                const rows = tableBody.querySelectorAll('tr');
                let visibleCount = 0;

                rows.forEach(row => {
                    // Skip the "no students found" row
                    if (row.querySelector('td[colspan]')) {
                        return;
                    }

                    const cells = row.querySelectorAll('td');
                    let rowText = '';

                    // Combine all cell text content for searching
                    cells.forEach(cell => {
                        // Skip the actions column (last column)
                        if (cell !== cells[cells.length - 1]) {
                            rowText += cell.textContent.toLowerCase() + ' ';
                        }
                    });

                    // Show/hide row based on search match
                    if (!searchTerm || rowText.includes(searchTerm)) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show "no results" message if no rows are visible
                showNoResultsMessage(visibleCount, searchTerm);

                // Update stats display
                updateSearchStats(visibleCount, searchTerm);
            }

            // Show no results message
            function showNoResultsMessage(visibleCount, searchTerm) {
                const tableBody = document.querySelector('tbody');
                let noResultsRow = tableBody.querySelector('.no-results-row');

                if (visibleCount === 0 && searchTerm) {
                    if (!noResultsRow) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-search fs-1 mb-2 d-block"></i>
                                    <p class="mb-0">No students found matching "${searchTerm}"</p>
                                </div>
                            </td>
                        `;
                        tableBody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }

            // Update search statistics
            function updateSearchStats(visibleCount, searchTerm) {
                const statsElement = document.querySelector('.text-muted.small');
                if (statsElement) {
                    if (searchTerm) {
                        statsElement.textContent = `Showing ${visibleCount} students matching "${searchTerm}"`;
                    } else {
                        // Reset to original pagination text when no search
                        const totalStudents = document.querySelectorAll('tbody tr:not(.no-results-row)').length;
                        statsElement.textContent = `Showing ${totalStudents} students`;
                    }
                }
            }

            // Enhanced fetch results function for pagination
            function fetchResults(url) {
                const controller = new AbortController();
                currentRequest = controller;

                fetch(url, {
                    signal: controller.signal,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                    .then(response => response.text())
                    .then(html => {
                        // Parse the response and update the table
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const newTableBody = doc.querySelector('tbody');
                        const currentTableBody = document.querySelector('tbody');

                        if (newTableBody && currentTableBody) {
                            // Add fade effect
                            currentTableBody.style.opacity = '0.7';
                            setTimeout(() => {
                                currentTableBody.innerHTML = newTableBody.innerHTML;
                                currentTableBody.style.opacity = '1';

                                // Re-attach delete button event listeners
                                attachDeleteButtonListeners();

                                // Apply search filter to new content
                                if (searchInput.value.trim()) {
                                    performRealTimeSearch();
                                }
                            }, 100);
                        }

                        // Update pagination and stats
                        updatePaginationAndStats(doc);
                    })
                    .catch(error => {
                        if (error.name !== 'AbortError') {
                            console.error('Search error:', error);
                            showSearchError();
                        }
                    })
                    .finally(() => {
                        hideLoading();
                        currentRequest = null;
                    });
            }

            // Update pagination and stats
            function updatePaginationAndStats(doc) {
                const newPagination = doc.querySelector('.d-flex.justify-content-between.align-items-center.mt-4');
                const currentPagination = document.querySelector('.d-flex.justify-content-between.align-items-center.mt-4');

                if (newPagination && currentPagination) {
                    currentPagination.innerHTML = newPagination.innerHTML;
                }
            }

            // Show search error
            function showSearchError() {
                const tableBody = document.querySelector('tbody');
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-danger">
                                <i class="bi bi-exclamation-triangle fs-1 mb-2 d-block"></i>
                                <p class="mb-0">Search failed. Please try again.</p>
                            </div>
                        </td>
                    </tr>
                `;
            }

            // Re-attach delete button event listeners after AJAX update
            function attachDeleteButtonListeners() {
                document.querySelectorAll('.delete-student-btn').forEach(button => {
                    button.addEventListener('click', function () {
                        const studentName = this.getAttribute('data-student-name');
                        const studentNameElement = document.getElementById('studentName');
                        if (studentNameElement) {
                            studentNameElement.textContent = studentName;
                        }
                        currentForm = this.closest('form');
                    });
                });
            }

            // Initialize
            toggleClearButton();

            // Fix dropdown positioning - Align with button
            const dropdownToggle = document.getElementById('studentFilterDropdown');
            if (dropdownToggle) {
                dropdownToggle.addEventListener('shown.bs.dropdown', function () {
                    const dropdownMenu = this.nextElementSibling;
                    if (dropdownMenu) {
                        // Get button position and dimensions
                        const buttonRect = this.getBoundingClientRect();
                        const containerRect = this.offsetParent.getBoundingClientRect();

                        // Calculate relative position
                        const leftPosition = buttonRect.left - containerRect.left;

                        // Apply positioning
                        dropdownMenu.style.position = 'absolute';
                        dropdownMenu.style.top = 'calc(100% + 8px)';
                        dropdownMenu.style.left = '0px';
                        dropdownMenu.style.right = 'auto';
                        dropdownMenu.style.bottom = 'auto';
                        dropdownMenu.style.transform = 'translateX(0)';
                        dropdownMenu.style.marginTop = '0';
                        dropdownMenu.style.minWidth = this.offsetWidth + 'px';
                    }
                });
            }

            // Checkbox Selection and Bulk Actions Functionality
            const selectAllCheckbox = document.getElementById('selectAll');
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
            const bulkAssignBtn = document.getElementById('bulkAssignBtn');
            const selectedCountSpan = document.getElementById('selectedCount');
            const assignSelectedCountSpan = document.getElementById('assignSelectedCount');
            const bulkDeleteCountSpan = document.getElementById('bulkDeleteCount');
            const bulkAssignCountSpan = document.getElementById('bulkAssignCount');
            const confirmBulkDeleteBtn = document.getElementById('confirmBulkDeleteBtn');

            // Select All functionality
            selectAllCheckbox.addEventListener('change', function() {
                studentCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActionButtons();
            });

            // Individual checkbox functionality
            studentCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectAllState();
                    updateBulkActionButtons();
                });
            });

            // Update Select All checkbox state
            function updateSelectAllState() {
                const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
                const totalBoxes = studentCheckboxes.length;

                if (checkedBoxes.length === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (checkedBoxes.length === totalBoxes) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                    selectAllCheckbox.checked = false;
                }
            }

            // Update bulk action buttons visibility and count
            function updateBulkActionButtons() {
                const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
                const count = checkedBoxes.length;

                if (count > 0) {
                    bulkDeleteBtn.style.display = 'inline-flex';
                    bulkAssignBtn.style.display = 'inline-flex';
                    selectedCountSpan.textContent = count;
                    assignSelectedCountSpan.textContent = count;
                    bulkDeleteCountSpan.textContent = count;
                    bulkAssignCountSpan.textContent = count;
                } else {
                    bulkDeleteBtn.style.display = 'none';
                    bulkAssignBtn.style.display = 'none';
                }
            }

            // Bulk delete confirmation
            confirmBulkDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
                const studentIds = Array.from(checkedBoxes).map(cb => cb.value);

                if (studentIds.length > 0) {
                    // Create a form to submit the bulk delete request
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '<?php echo e(route("admin.students.bulk-delete")); ?>';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '<?php echo e(csrf_token()); ?>';
                    form.appendChild(csrfToken);

                    // Add method override for DELETE
                    const methodField = document.createElement('input');
                    methodField.type = 'hidden';
                    methodField.name = '_method';
                    methodField.value = 'DELETE';
                    form.appendChild(methodField);

                    // Add student IDs
                    studentIds.forEach(id => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'student_ids[]';
                        input.value = id;
                        form.appendChild(input);
                    });

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                modal.hide();
            });

            // Bulk assignment functionality
            const bulkGradeLevel = document.getElementById('bulkGradeLevel');
            const sectionListContainer = document.getElementById('sectionListContainer');
            const sectionsList = document.getElementById('sectionsList');
            const selectedSectionId = document.getElementById('selectedSectionId');
            const bulkAssignStudentIds = document.getElementById('bulkAssignStudentIds');

            // All sections data (passed from backend)
            const allSections = <?php echo json_encode($sections, 15, 512) ?>;

            // Grade level change handler for section list
            bulkGradeLevel.addEventListener('change', function() {
                const selectedGrade = this.value;

                if (selectedGrade === '') {
                    sectionListContainer.style.display = 'none';
                    selectedSectionId.value = '';
                    return;
                }

                // Show section list container
                sectionListContainer.style.display = 'block';

                // Filter sections by grade level
                const filteredSections = allSections.filter(section => section.grade_level === selectedGrade);

                // Clear previous sections
                sectionsList.innerHTML = '';
                selectedSectionId.value = '';

                if (filteredSections.length === 0) {
                    sectionsList.innerHTML = '<div class="alert alert-warning small"><i class="bi bi-exclamation-triangle me-2"></i>No sections available for this grade level.</div>';
                    return;
                }

                // Create section cards
                filteredSections.forEach(section => {
                    const sectionCard = document.createElement('div');
                    sectionCard.className = 'section-card';
                    sectionCard.dataset.sectionId = section.id;

                    sectionCard.innerHTML = `
                        <div class="section-name">${section.name}</div>
                        <div class="section-info">${section.grade_level}</div>
                        <div class="section-students">${section.students_count || 0} students</div>
                    `;

                    // Add click handler
                    sectionCard.addEventListener('click', function() {
                        // Remove selected class from all cards
                        document.querySelectorAll('.section-card').forEach(card => {
                            card.classList.remove('selected');
                        });

                        // Add selected class to clicked card
                        this.classList.add('selected');

                        // Set hidden input value
                        selectedSectionId.value = this.dataset.sectionId;

                        console.log('Section selected:', section.name, 'ID:', section.id);
                    });

                    sectionsList.appendChild(sectionCard);
                });
            });

            // When bulk assign modal is shown, populate student IDs
            document.getElementById('bulkAssignModal').addEventListener('show.bs.modal', function() {
                const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
                const studentIds = Array.from(checkedBoxes).map(cb => cb.value);
                bulkAssignStudentIds.value = JSON.stringify(studentIds);
            });

            // Handle dropdown positioning to ensure they appear in front
            const dropdowns = document.querySelectorAll('.dropdown');
            console.log('Found dropdowns:', dropdowns.length);

            dropdowns.forEach((dropdown, index) => {
                const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                const dropdownMenu = dropdown.querySelector('.dropdown-menu');

                console.log(`Dropdown ${index}:`, {
                    toggle: !!dropdownToggle,
                    menu: !!dropdownMenu,
                    toggleId: dropdownToggle?.id
                });

                if (dropdownToggle && dropdownMenu) {
                    // Test basic click functionality
                    dropdownToggle.addEventListener('click', function(e) {
                        console.log('Dropdown button clicked:', this.id);
                    });

                    // When dropdown is about to be shown
                    dropdownToggle.addEventListener('show.bs.dropdown', function(e) {
                        // Don't prevent default - let Bootstrap handle the dropdown

                        // Ensure dropdown appears in front
                        dropdown.style.zIndex = '10001';
                        dropdownMenu.style.zIndex = '10002';

                        // Add a class to the parent row for higher z-index
                        const parentRow = dropdown.closest('tr');
                        if (parentRow) {
                            parentRow.classList.add('dropdown-active');
                            parentRow.style.zIndex = '10000';
                        }

                        console.log('Dropdown opening - z-index applied');
                    });

                    // When dropdown is shown (after animation)
                    dropdownToggle.addEventListener('shown.bs.dropdown', function() {
                        // Force reposition if needed
                        const rect = dropdownToggle.getBoundingClientRect();
                        const menuRect = dropdownMenu.getBoundingClientRect();

                        // Check if dropdown is going off-screen and adjust
                        if (menuRect.right > window.innerWidth) {
                            dropdownMenu.style.left = 'auto';
                            dropdownMenu.style.right = '0';
                        }

                        // Remove scrollbars from dropdown only
                        dropdownMenu.style.overflow = 'visible';
                        dropdownMenu.style.maxHeight = 'none';

                        console.log('Dropdown fully opened');
                    });

                    // When dropdown is hidden
                    dropdownToggle.addEventListener('hidden.bs.dropdown', function() {
                        // Reset z-index
                        dropdown.style.zIndex = '';
                        dropdownMenu.style.zIndex = '';

                        // Remove class from parent row
                        const parentRow = dropdown.closest('tr');
                        if (parentRow) {
                            parentRow.classList.remove('dropdown-active');
                            parentRow.style.zIndex = '';
                        }

                        console.log('Dropdown closed - z-index reset');
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\MedellinNHS\finalNani\Capstone\resources\views/admin/students/index.blade.php ENDPATH**/ ?>