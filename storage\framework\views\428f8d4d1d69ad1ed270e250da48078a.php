<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center min-vh-100 align-items-center">
        <div class="col-md-4">
            <div class="card shadow-lg border-0">
                <div class="card-body p-3">
                    <!-- Logo -->
                    <div class="text-center mb-3">
                        <img src="<?php echo e(asset('MedellinLogo.png')); ?>" alt="Logo" class="img-fluid mb-2" style="height: 60px;">
                        <h2 class="fw-bold text-primary mb-1" style="font-size: 1.5rem;">Welcome!</h2>
                        <p class="text-muted small">Please sign in to your account</p>
                    </div>

                    <?php if(session('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show py-2" role="alert">
                            <i class="bi bi-exclamation-circle-fill me-2"></i>
                            <?php echo e(session('error')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php $__errorArgs = ['access_denied'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="alert alert-warning alert-dismissible fade show py-2" role="alert">
                            <i class="bi bi-shield-exclamation me-2"></i>
                            <?php echo e($message); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show py-2" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <?php echo e(session('success')); ?>

                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo e(route('login')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="mb-3">
                            <label for="username" class="form-label text-muted small">ID Number</label>
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="bi bi-person text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0 <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="username" name="username" value="<?php echo e(old('username')); ?>" required autofocus
                                       placeholder="Enter your School ID">
                            </div>
                            <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-block">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label text-muted small">Password</label>
                            <div class="input-group shadow-sm">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="bi bi-lock text-muted"></i>
                                </span>
                                <input type="password" class="form-control border-start-0 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="password" name="password" required
                                       placeholder="Enter your password">
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-block">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                <label class="form-check-label text-muted small" for="remember">
                                    Remember me
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-2 mb-2 shadow-sm">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 6px;
    border: none;
    background: white;
    max-width: 360px;
    margin: 0 auto;
}
.shadow-lg {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}
.card-body {
    padding: 1.5rem !important;
}
.input-group-text {
    border-right: none;
    background-color: white;
    color: #6c757d;
    padding: 0.4rem 0.6rem;
    font-size: 0.875rem;
}
.form-control {
    border-left: none;
    padding: 0.4rem 0.6rem;
    background-color: white;
    font-size: 0.875rem;
    height: 36px;
}
.form-control:focus {
    box-shadow: none;
    border-color: #dee2e6;
    background-color: white;
}
.input-group:focus-within {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
    border-radius: 0.375rem;
}
.input-group:focus-within .input-group-text,
.input-group:focus-within .form-control {
    border-color: #86b7fe;
}
.btn-primary {
    background-color: #4267B2;
    border-color: #4267B2;
    font-weight: 500;
    padding: 0.4rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    height: 36px;
}
.btn-primary:hover {
    background-color: #385499;
    border-color: #385499;
}
.form-label {
    color: #495057;
    font-size: 0.813rem;
    margin-bottom: 0.25rem;
}
.text-muted {
    color: #6c757d !important;
}
.form-check-input:checked {
    background-color: #4267B2;
    border-color: #4267B2;
}
.alert {
    border-radius: 4px;
    font-size: 0.813rem;
    padding: 0.5rem 0.75rem;
}
.mb-3 {
    margin-bottom: 0.75rem !important;
}
.mb-4 {
    margin-bottom: 1rem !important;
}
img.img-fluid {
    height: 50px !important;
}
h4.fw-bold {
    font-size: 1.25rem;
}
.small {
    font-size: 0.813rem;
}
.form-check-label {
    font-size: 0.813rem;
}
</style>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.login', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\MedellinNHS\finalNani\Capstone\resources\views/auth/login.blade.php ENDPATH**/ ?>