<!-- resources/views/layouts/sidebar.blade.php -->

<!-- Mobile Overlay -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Sidebar Toggle Button -->
<button class="sidebar-toggle-btn" id="sidebarToggle" type="button">
    <i class="bi bi-list"></i>
</button>

<div class="materio-sidebar d-flex flex-column align-items-start pt-3 px-3" id="adminSidebar">
    <!-- Logo -->
    <div class="w-100 d-flex align-items-center mb-2 logo-section">
        <img src="{{ asset('MedellinLogo.png') }}" alt="Logo" class="materio-logo me-3">
        <span class="sidebar-appname">MNHS Student Information Portal</span>
    </div>

    <!-- Main Navigation -->
    <div class="w-100 mt-1">
        <div class="sidebar-section-title">MAIN</div>
        <ul class="nav flex-column materio-nav">
            <li class="nav-item mb-1">
                <a class="nav-link d-flex align-items-center materio-link @if(request()->routeIs('admin.dashboard')) active @endif" href="{{ route('admin.dashboard') }}">
                    <i class="bi bi-speedometer2 me-3"></i> Dashboard
                </a>
            </li>
            <li class="nav-item mb-1">
                <a class="nav-link d-flex align-items-center materio-link @if(request()->routeIs('admin.students.index') || request()->routeIs('admin.teachers.index')) active @endif" data-bs-toggle="collapse" href="#usersSubmenu" role="button">
                    <i class="bi bi-people-fill me-3"></i> User Management
                    <i class="bi bi-chevron-down ms-auto small"></i>
                </a>
                <div class="collapse ps-4" id="usersSubmenu">
                    <a href="{{ route('admin.students.index') }}" class="nav-link materio-sublink">Students</a>
                    <a href="{{ route('admin.teachers.index') }}" class="nav-link materio-sublink">Teachers</a>
                </div>
            </li>
            <li class="nav-item mb-1">
                <a class="nav-link d-flex align-items-center materio-link @if(request()->routeIs('admin.subjects.*')) active @endif" data-bs-toggle="collapse" href="#subjectsSubmenu" role="button">
                    <i class="bi bi-book-fill me-3"></i> Curriculum
                    <i class="bi bi-chevron-down ms-auto small"></i>
                </a>
                <div class="collapse ps-4" id="subjectsSubmenu">
                    <a href="{{ route('admin.subjects.index') }}" class="nav-link materio-sublink">Learning Areas</a>
                    <a href="{{ route('admin.subjects.plan') }}" class="nav-link materio-sublink">Curriculum Structure</a>
                </div>
            </li>
            <li class="nav-item mb-1">
                <a class="nav-link d-flex align-items-center materio-link @if(request()->routeIs('admin.sections.index')) active @endif" href="{{ route('admin.sections.index') }}">
                    <i class="bi bi-building me-3"></i> Class Sections
                </a>
            </li>
            <li class="nav-item mb-1">
                <a class="nav-link d-flex align-items-center materio-link @if(request()->routeIs('admin.events.index')) active @endif" href="{{ route('admin.events.index') }}">
                    <i class="bi bi-calendar-event me-3"></i> School Events
                </a>
            </li>
        </ul>
    </div>
</div>

<style>
/* Sidebar Toggle Button */
.sidebar-toggle-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1060;
    background: #fff;
    border: none;
    border-radius: 12px;
    width: 48px;
    height: 48px;
    display: none;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    color: #5A5A89;
    font-size: 20px;
}

.sidebar-toggle-btn:hover {
    background: #f8f9fa;
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.sidebar-toggle-btn:active {
    transform: scale(0.95);
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Main Sidebar */
.materio-sidebar {
    width: 260px;
    min-height: 100vh;
    background: #fff;
    border-radius: 0 2rem 2rem 0;
    box-shadow: 2px 0 24px rgba(80, 80, 160, 0.10);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1055;
    transition: all 0.3s ease;
    color: #5A5A89;
    transform: translateX(0);
}

/* Sidebar States */
.materio-sidebar.collapsed {
    width: 70px;
    border-radius: 0 1rem 1rem 0;
}

.materio-sidebar.mobile-hidden {
    transform: translateX(-100%);
}

/* Logo Section */
.logo-section {
    margin-bottom: 1.2rem;
    padding: 0.5rem 0 0.5rem 0.5rem;
    justify-content: flex-start;
    background: none;
    border-radius: 1.5rem 1.5rem 0 0;
    transition: all 0.3s ease;
}

.materio-logo {
    height: 72px;
    width: 72px;
    object-fit: contain;
    border-radius: 16px;
    background: #f5f6fa;
    box-shadow: 0 2px 8px rgba(80,80,160,0.06);
    padding: 8px;
    transition: all 0.3s ease;
}

.sidebar-appname {
    font-family: 'Poppins', 'Segoe UI', Arial, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    color: #4B4B8F;
    letter-spacing: 0.04em;
    text-shadow: 0 2px 8px rgba(80,80,160,0.06);
    transition: all 0.3s ease;
    opacity: 1;
    white-space: nowrap;
    overflow: hidden;
}

/* Section Title */
.sidebar-section-title {
    font-size: 0.75rem;
    font-weight: 700;
    color: #b0b3c6;
    text-transform: uppercase;
    letter-spacing: 0.08em;
    margin-bottom: 0.5rem;
    margin-top: 0.2rem;
    padding-left: 0.5rem;
    transition: all 0.3s ease;
    opacity: 1;
    white-space: nowrap;
    overflow: hidden;
}

/* Navigation */
.materio-nav {
    width: 100%;
}

.materio-link {
    color: #5A5A89;
    font-weight: 500;
    border-radius: 2rem;
    padding: 0.65rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1.05rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
}

.materio-link.active, .materio-link:hover {
    background: linear-gradient(90deg, #e3e6ff 0%, #f5f6fa 100%);
    color: #6f4ef2;
    font-weight: 600;
}

.materio-link i {
    min-width: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.materio-link .ms-auto {
    transition: all 0.3s ease;
}

/* Sublinks */
.materio-sublink {
    color: #a0a3b6;
    font-size: 0.98rem;
    margin-left: 1.5rem;
    border-radius: 1rem;
    transition: all 0.3s ease;
    padding: 0.45rem 1rem;
    text-decoration: none;
    display: block;
    white-space: nowrap;
    overflow: hidden;
}

.materio-sublink:hover, .materio-sublink.active {
    background: #f5f6fa;
    color: #6f4ef2;
}

/* Collapsed State Styles */
.materio-sidebar.collapsed .sidebar-appname,
.materio-sidebar.collapsed .sidebar-section-title {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.materio-sidebar.collapsed .materio-logo {
    height: 40px;
    width: 40px;
    margin: 0 auto;
}

.materio-sidebar.collapsed .logo-section {
    justify-content: center;
    padding: 0.5rem 0.25rem;
}

.materio-sidebar.collapsed .materio-link {
    padding: 0.65rem 0.5rem;
    justify-content: center;
    border-radius: 1rem;
}

.materio-sidebar.collapsed .materio-link .ms-auto,
.materio-sidebar.collapsed .collapse {
    display: none !important;
}

.materio-sidebar.collapsed .materio-link i {
    margin-right: 0;
}

/* Responsive Breakpoints */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .materio-sidebar {
        width: 280px;
    }
}

/* Desktop (992px to 1199px) */
@media (min-width: 992px) and (max-width: 1199px) {
    .materio-sidebar {
        width: 260px;
    }
}

/* Tablet (768px to 991px) */
@media (min-width: 768px) and (max-width: 991px) {
    .sidebar-toggle-btn {
        display: flex;
    }

    .materio-sidebar {
        width: 260px;
        transform: translateX(-100%);
    }

    .materio-sidebar.show {
        transform: translateX(0);
    }
}

/* Mobile (576px to 767px) */
@media (min-width: 576px) and (max-width: 767px) {
    .sidebar-toggle-btn {
        display: flex;
    }

    .materio-sidebar {
        width: 280px;
        transform: translateX(-100%);
        border-radius: 0 1.5rem 1.5rem 0;
    }

    .materio-sidebar.show {
        transform: translateX(0);
    }
}

/* Small Mobile (up to 575px) */
@media (max-width: 575px) {
    .sidebar-toggle-btn {
        display: flex;
        top: 15px;
        left: 15px;
        width: 44px;
        height: 44px;
        font-size: 18px;
    }

    .materio-sidebar {
        width: 100%;
        max-width: 300px;
        transform: translateX(-100%);
        border-radius: 0 1rem 1rem 0;
    }

    .materio-sidebar.show {
        transform: translateX(0);
    }

    .sidebar-appname {
        font-size: 0.9rem;
    }

    .materio-link {
        font-size: 1rem;
        padding: 0.6rem 1rem;
    }

    .materio-logo {
        height: 60px;
        width: 60px;
    }
}

/* Animation for smooth transitions */
@media (prefers-reduced-motion: no-preference) {
    .materio-sidebar,
    .sidebar-overlay,
    .sidebar-toggle-btn,
    .materio-link,
    .sidebar-appname,
    .sidebar-section-title,
    .materio-logo {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .materio-sidebar {
        border: 2px solid #000;
    }

    .materio-link {
        border: 1px solid transparent;
    }

    .materio-link:hover,
    .materio-link.active {
        border-color: #000;
    }
}

/* Focus styles for accessibility */
.sidebar-toggle-btn:focus,
.materio-link:focus,
.materio-sublink:focus {
    outline: 2px solid #6f4ef2;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .materio-sidebar,
    .sidebar-toggle-btn,
    .sidebar-overlay {
        display: none !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('adminSidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    // Check if elements exist
    if (!sidebar || !sidebarToggle || !sidebarOverlay) {
        console.warn('Sidebar elements not found');
        return;
    }

    // Function to check if we're in mobile/tablet mode
    function isMobileMode() {
        return window.innerWidth < 992;
    }

    // Function to update sidebar state based on screen size
    function updateSidebarState() {
        if (isMobileMode()) {
            // Mobile/Tablet mode
            sidebar.classList.remove('collapsed');
            sidebar.classList.add('mobile-hidden');
        } else {
            // Desktop mode
            sidebar.classList.remove('mobile-hidden', 'show');
            sidebarOverlay.classList.remove('show');

            // Check if sidebar should be collapsed (optional - you can remove this if you don't want auto-collapse)
            // sidebar.classList.toggle('collapsed', window.innerWidth < 1200);
        }
    }

    // Function to toggle sidebar
    function toggleSidebar() {
        if (isMobileMode()) {
            // Mobile/Tablet toggle
            const isVisible = sidebar.classList.contains('show');

            if (isVisible) {
                hideSidebar();
            } else {
                showSidebar();
            }
        } else {
            // Desktop toggle (collapse/expand)
            sidebar.classList.toggle('collapsed');

            // Dispatch custom event to notify topbar
            const event = new CustomEvent('sidebarToggled', {
                detail: {
                    collapsed: sidebar.classList.contains('collapsed'),
                    timestamp: Date.now()
                }
            });
            document.dispatchEvent(event);

            console.log('🎯 Sidebar toggled:', sidebar.classList.contains('collapsed') ? 'COLLAPSED' : 'EXPANDED');
        }
    }

    // Function to show sidebar (mobile)
    function showSidebar() {
        sidebar.classList.add('show');
        sidebar.classList.remove('mobile-hidden');
        sidebarOverlay.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    // Function to hide sidebar (mobile)
    function hideSidebar() {
        sidebar.classList.remove('show');
        sidebar.classList.add('mobile-hidden');
        sidebarOverlay.classList.remove('show');
        document.body.style.overflow = ''; // Restore scrolling
    }

    // Event listeners
    sidebarToggle.addEventListener('click', toggleSidebar);

    // Close sidebar when clicking overlay
    sidebarOverlay.addEventListener('click', function() {
        if (isMobileMode()) {
            hideSidebar();
        }
    });

    // Close sidebar when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && isMobileMode() && sidebar.classList.contains('show')) {
            hideSidebar();
        }
    });

    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            updateSidebarState();
        }, 150);
    });

    // Handle dropdown menus in collapsed state
    const dropdownToggles = sidebar.querySelectorAll('[data-bs-toggle="collapse"]');
    dropdownToggles.forEach(function(toggle) {
        toggle.addEventListener('click', function(e) {
            if (!isMobileMode() && sidebar.classList.contains('collapsed')) {
                e.preventDefault();
                e.stopPropagation();

                // Create tooltip or mini-menu for collapsed state
                // You can implement this if needed
                console.log('Dropdown clicked in collapsed state');
            }
        });
    });

    // Initialize sidebar state
    updateSidebarState();

    // Handle touch gestures for mobile (optional enhancement)
    let touchStartX = 0;
    let touchEndX = 0;

    document.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
    });

    document.addEventListener('touchend', function(e) {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipeGesture();
    });

    function handleSwipeGesture() {
        if (!isMobileMode()) return;

        const swipeThreshold = 50;
        const swipeDistance = touchEndX - touchStartX;

        // Swipe right to open sidebar (only if starting from left edge)
        if (swipeDistance > swipeThreshold && touchStartX < 50 && !sidebar.classList.contains('show')) {
            showSidebar();
        }

        // Swipe left to close sidebar
        if (swipeDistance < -swipeThreshold && sidebar.classList.contains('show')) {
            hideSidebar();
        }
    }

    // Accessibility improvements
    sidebarToggle.setAttribute('aria-label', 'Toggle navigation menu');
    sidebarToggle.setAttribute('aria-expanded', 'false');

    // Update aria-expanded when sidebar state changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                const isExpanded = sidebar.classList.contains('show') || (!isMobileMode() && !sidebar.classList.contains('collapsed'));
                sidebarToggle.setAttribute('aria-expanded', isExpanded.toString());
            }
        });
    });

    observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });
});
</script>