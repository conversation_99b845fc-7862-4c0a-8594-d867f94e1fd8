<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Student Sidebar Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 250px;
            padding: 1.5rem;
            min-height: 100vh;
        }
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
        }
        .nav-link {
            color: #000;
        }
        .nav-link:hover, .nav-link.active {
            background-color: #f1f1f1;
            font-weight: bold;
        }
        .topbar {
            position: fixed;
            top: 24px;
            left: 270px;
            right: 32px;
            z-index: 1050;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            background: #fff;
            border-radius: 1.5rem;
            box-shadow: 0 4px 24px rgba(80,80,160,0.10);
            padding: 0 2rem 0 2rem;
            width: auto;
            min-width: 320px;
        }
        .main-content {
            margin-top: 96px;
        }
        .topbar-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .topbar-profile img {
            height: 40px;
            width: 40px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #ff6aab;
            background-color: #f8f9fa;
        }
        .topbar-profile .default-avatar {
            height: 40px;
            width: 40px;
            border-radius: 50%;
            border: 2px solid #ff6aab;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }
        .topbar-profile .fw-bold {
            font-size: 1rem;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .topbar-profile small {
            color: #888;
            font-size: 0.85rem;
        }
        /* Profile Dropdown Container Positioning */
        .topbar .dropdown {
            position: relative;
        }

        /* Enhanced Profile Dropdown Styling */
        .dropdown-menu {
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            padding: 12px;
            margin-top: 8px !important;
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.98);
            min-width: 200px;
            transform-origin: top right;
            transition: opacity 0.15s ease, transform 0.15s ease;
            animation: dropdownFadeIn 0.2s ease-out forwards;
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
            left: auto !important;
            z-index: 1050;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Dropdown Arrow Pointer - Points to Profile Picture */
        .dropdown-menu-end::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 50px; /* Adjusted to point to center of profile picture (40px width + 10px margin) */
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.98);
            transform: rotate(45deg);
            border-radius: 2px;
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        /* Mobile responsive arrow positioning */
        @media (max-width: 767px) {
            .dropdown-menu-end::before {
                right: 25px; /* Adjusted for mobile when text is hidden */
            }
        }

        .dropdown-item {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 4px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .dropdown-item:last-child {
            margin-bottom: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: #4f46e5;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }

        .dropdown-item:active, .dropdown-item:focus {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(129, 140, 248, 0.08));
            color: #4f46e5;
        }

        .dropdown-item.text-danger:hover {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(254, 226, 226, 0.5));
            color: #dc2626;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
        }

        .dropdown-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .dropdown-item:hover i {
            transform: scale(1.05);
        }

        .dropdown-divider {
            margin: 8px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            opacity: 1;
        }

        /* Remove blue border and ensure consistent profile dropdown styling */
        .topbar-profile.dropdown-toggle {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle:focus {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle:active {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle[aria-expanded="true"] {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }
        @media (max-width: 991px) {
            .topbar {
                left: 80px;
                right: 8px;
                padding: 0 1rem;
            }
            .main-content {
                margin-top: 80px;
            }
        }
    </style>
</head>
<body>

    @include('layouts.studentSidebar')

    <!-- Topbar -->
    <div class="topbar">
        <div class="dropdown">
            <a href="#" class="d-flex align-items-center text-decoration-none text-dark topbar-profile dropdown-toggle" id="profileDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                @if(Auth::user()->hasProfilePicture())
                    <img src="{{ Auth::user()->profile_picture_url }}" alt="User">
                @else
                    <div class="default-avatar">
                        {{ strtoupper(substr(Auth::user()->first_name, 0, 1)) }}{{ strtoupper(substr(Auth::user()->last_name, 0, 1)) }}
                    </div>
                @endif
                <div class="ms-2 d-none d-md-block">
                    <span class="fw-bold" title="{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</span>
                    <small class="d-block text-muted">{{ ucfirst(Auth::user()->role) }}</small>
                </div>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
                <li>
                    <a class="dropdown-item d-flex align-items-center" href="{{ route('student.profile') }}">
                        <i class="bi bi-person me-2"></i> My Profile
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <form action="{{ route('logout') }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="dropdown-item d-flex align-items-center text-danger">
                            <i class="bi bi-box-arrow-right me-2"></i> Logout
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            @yield('content')
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Fix profile dropdown positioning
            const profileDropdown = document.getElementById('profileDropdown');
            if (profileDropdown) {
                profileDropdown.addEventListener('shown.bs.dropdown', function () {
                    const dropdownMenu = this.nextElementSibling;
                    if (dropdownMenu) {
                        dropdownMenu.style.position = 'absolute';
                        dropdownMenu.style.top = '100%';
                        dropdownMenu.style.right = '0';
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.transform = 'translateY(8px)';
                        dropdownMenu.style.marginTop = '8px';
                    }
                });
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
