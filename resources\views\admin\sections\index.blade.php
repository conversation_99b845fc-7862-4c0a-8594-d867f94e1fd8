@extends('layouts.app')

@section('content')
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="fw-bold mb-1 text-primary">Sections Management</h2>
                <p class="text-muted mb-0 small">Manage sections for each grade level</p>
            </div>
            <div class="d-flex gap-2">
                <div class="dropdown position-relative">
                    <button class="btn btn-secondary btn-action dropdown-toggle {{ $selectedGrade ? 'filter-active' : '' }}" type="button" id="gradeLevelDropdown" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true">
                        <i class="bi bi-mortarboard me-2"></i>
                        {{ $selectedGrade ? $selectedGrade : 'All Grades' }}
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="gradeLevelDropdown">
                        <li>
                            <a class="dropdown-item {{ !$selectedGrade ? 'active' : '' }}" href="{{ route('admin.sections.index') }}">
                                <i class="bi bi-grid-3x3-gap me-2"></i> All Grades
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        @foreach($gradeLevels as $grade)
                            <li>
                                <a class="dropdown-item {{ $selectedGrade == $grade ? 'active' : '' }}" 
                                   href="{{ route('admin.sections.index', ['grade_level' => $grade]) }}">
                                    <i class="bi bi-mortarboard me-2"></i> {{ $grade }}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </div>
                <a href="{{ route('admin.sections.create', ['grade_level' => $selectedGrade]) }}"
                   class="btn btn-primary btn-action {{ !$selectedGrade ? 'disabled' : '' }}"
                   @if(!$selectedGrade) onclick="return false;" @endif>
                    <i class="bi bi-plus-lg me-2"></i>
                    Add Section
                </a>
            </div>
        </div>

        <!-- Sections Table -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary">Section ID</th>
                                <th class="text-primary">Name</th>
                                <th class="text-primary">Grade Level</th>
                                <th class="text-primary">Adviser</th>
                                <th class="text-primary">Students</th>
                                <th class="text-primary text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sections as $section)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-hash text-primary"></i>
                                            </div>
                                            <span class="fw-medium">{{ $section->section_id }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-book text-info"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $section->name }}</div>
                                                @if($section->description)
                                                    <div class="small text-muted">{{ Str::limit($section->description, 30) }}</div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-mortarboard me-1"></i>
                                            {{ $section->grade_level }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($section->adviser)
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-success bg-opacity-10 rounded-circle me-2">
                                                    <i class="bi bi-person-fill text-success"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-medium">{{ $section->adviser->last_name }}, {{ $section->adviser->first_name }}</div>
                                                    @if($section->adviser->email)
                                                        <div class="small text-muted">{{ $section->adviser->email }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">
                                                <i class="bi bi-person-x me-1"></i> Not Assigned
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-people-fill text-warning"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium">{{ $section->students_count ?? 0 }}</div>
                                                <div class="small text-muted">Enrolled</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-end gap-2">
                                            <a href="{{ route('admin.sections.students', $section) }}"
                                                class="btn btn-sm btn-outline-info btn-table-action" title="View Students">
                                                <i class="bi bi-people"></i>
                                            </a>
                                            <a href="{{ route('admin.sections.edit', $section) }}"
                                                class="btn btn-sm btn-outline-primary btn-table-action" title="Edit Section">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <form action="{{ route('admin.sections.destroy', $section) }}" method="POST"
                                                class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-outline-danger btn-table-action"
                                                    onclick="return confirm('Are you sure you want to delete this section?')"
                                                    title="Delete Section">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-inbox-fill fs-2 d-block mb-2"></i>
                                            No sections found
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div class="text-muted small">
                        Showing {{ $sections->firstItem() ?? 0 }} to {{ $sections->lastItem() ?? 0 }} of {{ $sections->total() ?? 0 }} entries
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        @if($sections->hasPages())
                            @if($sections->onFirstPage())
                                <button class="btn btn-secondary btn-action pagination-btn" disabled>
                                    <i class="bi bi-chevron-left me-2"></i>
                                    Previous
                                </button>
                            @else
                                <a href="{{ $sections->previousPageUrl() }}" class="btn btn-secondary btn-action pagination-btn">
                                    <i class="bi bi-chevron-left me-2"></i>
                                    Previous
                                </a>
                            @endif

                            @if($sections->hasMorePages())
                                <a href="{{ $sections->nextPageUrl() }}" class="btn btn-primary btn-action pagination-btn">
                                    Next
                                    <i class="bi bi-chevron-right ms-2"></i>
                                </a>
                            @else
                                <button class="btn btn-secondary btn-action pagination-btn" disabled>
                                    Next
                                    <i class="bi bi-chevron-right ms-2"></i>
                                </button>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Button Styling to Match Admin Students/Teachers */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            box-sizing: border-box;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-danger.btn-action {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-danger.btn-action:hover {
            background: linear-gradient(135deg, #b91c1c, #dc2626);
            box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        /* Table Action Buttons */
        .btn-table-action {
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            transition: var(--transition);
            min-width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-table-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .pagination-btn {
            min-width: 120px;
        }

        .pagination-btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Disabled button styling to match events page */
        .btn-action.disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
            pointer-events: none;
        }

        .btn-action.disabled:hover {
            opacity: 0.65;
            transform: none !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .card.shadow-sm {
            background-color: #f8f9fa;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-sm i {
            font-size: 1rem;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
        }

        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
        }

        .alert-success {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .btn-xs {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            min-width: 32px;
            height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-xs i {
            font-size: 0.75rem;
        }

        .alert i {
            font-size: 1.1rem;
        }

        /* Dropdown Container Stability */
        .dropdown {
            position: relative !important;
            z-index: 1;
            display: inline-block;
        }

        .dropdown-toggle {
            position: relative;
            z-index: 2;
        }

        /* Force dropdown positioning - Align with button */
        .dropdown.show #gradeLevelDropdown + .dropdown-menu {
            display: block !important;
            position: absolute !important;
            top: calc(100% + 8px) !important;
            left: 0 !important;
            transform: translateX(0) !important;
            min-width: 200px;
        }

        /* Enhanced Filter Dropdown Styling - Only for filter dropdowns */
        #gradeLevelDropdown + .dropdown-menu {
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            padding: 12px;
            margin-top: 8px !important;
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.98);
            min-width: 200px;
            width: max-content;
            transform-origin: top left;
            transition: opacity 0.15s ease, transform 0.15s ease;
            position: absolute !important;
            top: calc(100% + 4px) !important;
            left: 0 !important;
            right: auto !important;
            bottom: auto !important;
            z-index: 1050;
            will-change: transform, opacity;
            transform: translateX(0) !important;
        }

        #gradeLevelDropdown + .dropdown-menu.show {
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Filter Dropdown Arrow Pointer - Points to Right Side of Button */
        #gradeLevelDropdown + .dropdown-menu::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 20px; /* Positioned on the right side for consistency */
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.98);
            transform: rotate(45deg);
            border-radius: 2px;
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        .dropdown-item {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 4px;
            transition: var(--transition);
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .dropdown-item:last-child {
            margin-bottom: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: var(--primary-dark);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }

        .dropdown-item.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            transform: translateX(1px);
        }

        .dropdown-item.active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .dropdown-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            transition: var(--transition);
        }

        .dropdown-item:hover i {
            transform: scale(1.05);
        }

        .dropdown-item.active i {
            color: white;
        }

        .dropdown-divider {
            margin: 8px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            opacity: 1;
        }

        /* Enhanced Dropdown Toggle Button */
        .dropdown-toggle::after {
            margin-left: 8px;
            transition: var(--transition);
        }

        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .dropdown-toggle:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
        }

        /* Dropdown Button Enhancements */
        .btn-secondary.btn-action.dropdown-toggle {
            position: relative;
            overflow: hidden;
        }

        .btn-secondary.btn-action.dropdown-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-secondary.btn-action.dropdown-toggle:hover::before {
            left: 100%;
        }

        /* Filter Active State */
        .btn-secondary.btn-action.dropdown-toggle.filter-active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            color: white;
            border-color: var(--primary-dark);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active i {
            color: white;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .btn-action {
                padding: 10px 16px;
                font-size: 13px;
                min-width: auto;
            }

            .btn-table-action {
                padding: 6px 8px;
                min-width: 32px;
                height: 32px;
                font-size: 11px;
            }

            .pagination-btn {
                min-width: 100px;
                padding: 8px 12px;
                font-size: 12px;
            }

            /* Enhanced Filter Dropdown Mobile Responsive */
            #gradeLevelDropdown + .dropdown-menu {
                border-radius: 12px;
                padding: 8px;
                min-width: 180px;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }

            .dropdown-item {
                padding: 10px 12px;
                border-radius: 8px;
                font-size: 13px;
            }

            .dropdown-item i {
                width: 18px;
                height: 18px;
                font-size: 12px;
                margin-right: 10px;
            }

            .dropdown-toggle::after {
                margin-left: 6px;
            }

            /* Enhanced Filter Dropdown Mobile Responsive Arrow */
            #gradeLevelDropdown + .dropdown-menu::before {
                right: 15px; /* Adjusted for mobile when button is smaller */
            }
        }

        @media (max-width: 576px) {
            #gradeLevelDropdown + .dropdown-menu {
                min-width: 160px;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Fix dropdown positioning - Align with button
            const dropdownToggle = document.getElementById('gradeLevelDropdown');
            if (dropdownToggle) {
                dropdownToggle.addEventListener('shown.bs.dropdown', function () {
                    const dropdownMenu = this.nextElementSibling;
                    if (dropdownMenu) {
                        // Get button position and dimensions
                        const buttonRect = this.getBoundingClientRect();
                        const containerRect = this.offsetParent.getBoundingClientRect();

                        // Calculate relative position
                        const leftPosition = buttonRect.left - containerRect.left;

                        // Apply positioning
                        dropdownMenu.style.position = 'absolute';
                        dropdownMenu.style.top = 'calc(100% + 8px)';
                        dropdownMenu.style.left = '0px';
                        dropdownMenu.style.right = 'auto';
                        dropdownMenu.style.bottom = 'auto';
                        dropdownMenu.style.transform = 'translateX(0)';
                        dropdownMenu.style.marginTop = '0';
                        dropdownMenu.style.minWidth = this.offsetWidth + 'px';
                    }
                });
            }
        });
    </script>
@endsection