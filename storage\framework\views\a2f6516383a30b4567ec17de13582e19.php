<?php $__env->startSection('content'); ?>
    <div class="container-fluid py-3">
        <!-- Header Section -->
        <div class="row mb-4">
            <!-- Title Section -->
            <div class="col-12 mb-3">
                <div>
                    <h2 class="fw-bold mb-1 text-primary">Teacher Management</h2>
                    <p class="text-muted mb-0 small">Manage and monitor teacher records and information</p>
                </div>
            </div>

            <!-- Search and Action Buttons Section -->
            <div class="col-12">
                <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap">
                    <!-- Left Side: Search and Filter -->
                    <div class="d-flex align-items-center gap-3 flex-wrap">
                        <!-- Enhanced Search Container -->
                        <div class="search-container position-relative" style="min-width: 300px;">
                            <div class="search-input-wrapper">
                                <i class="bi bi-search search-icon"></i>
                                <input type="text" name="search" class="search-input" placeholder="Search teachers by name, employee ID, or subject..."
                                    value="<?php echo e(request('search')); ?>">
                                <div class="search-loading-spinner" style="display: none;">
                                    <div class="spinner"></div>
                                </div>
                                <button type="button" class="search-clear-btn" style="display: none;">
                                    <i class="bi bi-x-circle-fill"></i>
                                </button>
                            </div>
                            <div class="search-suggestions" style="display: none;">
                                <!-- Dynamic search suggestions will appear here -->
                            </div>
                        </div>

                        <!-- Grade Level Filter Dropdown -->
                        <div class="dropdown position-relative">
                            <button class="btn btn-secondary btn-action dropdown-toggle <?php echo e(request('grade_level') ? 'filter-active' : ''); ?>" type="button" id="teacherFilterDropdown"
                                data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true">
                                <i class="bi bi-funnel me-2"></i>
                                <?php echo e(request('grade_level') ? request('grade_level') : 'All Teachers'); ?>

                            </button>
                            <ul class="dropdown-menu" aria-labelledby="teacherFilterDropdown">
                                <li>
                                    <a class="dropdown-item <?php echo e(!request('grade_level') ? 'active' : ''); ?>"
                                        href="<?php echo e(route('admin.teachers.index', ['search' => request('search')])); ?>">
                                        <i class="bi bi-grid-3x3-gap me-2"></i> All Teachers
                                    </a>
                                </li>
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <?php for($i = 7; $i <= 12; $i++): ?>
                                    <li>
                                        <a class="dropdown-item <?php echo e(request('grade_level') == 'Grade ' . $i ? 'active' : ''); ?>"
                                            href="<?php echo e(route('admin.teachers.index', ['grade_level' => 'Grade ' . $i, 'search' => request('search')])); ?>">
                                            <i class="bi bi-mortarboard me-2"></i> Grade <?php echo e($i); ?>

                                        </a>
                                    </li>
                                <?php endfor; ?>
                            </ul>
                        </div>
                    </div>

                    <!-- Right Side: Action Buttons -->
                    <div class="d-flex gap-2 flex-wrap">
                        <button type="button" class="btn btn-success btn-action" data-bs-toggle="modal" data-bs-target="#uploadCsvModal">
                            <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                            Import CSV
                        </button>
                        <a href="<?php echo e(route('admin.teachers.create')); ?>" class="btn btn-primary btn-action">
                            <i class="bi bi-plus-lg me-2"></i>
                            Add Teacher
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="card shadow-lg border-0">
            <div class="card-body p-4">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="bg-light">
                            <tr>
                                <th class="text-primary">ID</th>
                                <th class="text-primary">Name</th>
                                <th class="text-primary">Email</th>
                                <th class="text-primary">Status</th>
                                <th class="text-primary text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person-badge text-primary"></i>
                                            </div>
                                            <span class="fw-medium"><?php echo e($teacher->teacher?->employee_id ?? 'N/A'); ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person text-info"></i>
                                            </div>
                                            <div>
                                                <div class="fw-medium"><?php echo e($teacher->formal_name); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-warning bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-envelope text-warning"></i>
                                            </div>
                                            <a href="mailto:<?php echo e($teacher->email); ?>" class="text-decoration-none">
                                                <?php echo e($teacher->email); ?>

                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo e($teacher->teacher?->status === 'active' ? 'success' : 
                                            ($teacher->teacher?->status === 'inactive' ? 'danger' : 
                                            ($teacher->teacher?->status === 'on_leave' ? 'warning' : 'secondary'))); ?>">
                                            <i class="bi bi-<?php echo e($teacher->teacher?->status === 'active' ? 'check-circle' : 
                                                ($teacher->teacher?->status === 'inactive' ? 'x-circle' : 
                                                ($teacher->teacher?->status === 'on_leave' ? 'pause-circle' : 'question-circle'))); ?> me-1"></i>
                                            <?php echo e(ucfirst(str_replace('_', ' ', $teacher->teacher?->status ?? 'inactive'))); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-end gap-2">
                                            <!-- View Button (Primary Action) -->
                                            <a href="<?php echo e(route('admin.teachers.show', $teacher->id)); ?>"
                                                class="btn btn-sm btn-outline-info btn-table-action" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <!-- Edit Button (Secondary Action) -->
                                            <a href="<?php echo e(route('admin.teachers.edit', $teacher->id)); ?>"
                                                class="btn btn-sm btn-outline-primary btn-table-action" title="Edit Teacher">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <!-- Delete Button (Destructive Action) -->
                                            <button type="button" class="btn btn-sm btn-outline-danger btn-table-action"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteModal<?php echo e($teacher->id); ?>"
                                                title="Delete Teacher">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Delete Confirmation Modal -->
                                        <div class="modal fade" id="deleteModal<?php echo e($teacher->id); ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo e($teacher->id); ?>" aria-hidden="true">
                                            <div class="modal-dialog modal-dialog-centered">
                                                <div class="modal-content">
                                                    <div class="modal-header border-0">
                                                        <h5 class="modal-title" id="deleteModalLabel<?php echo e($teacher->id); ?>">
                                                            <i class="bi bi-exclamation-triangle-fill text-danger me-2"></i>
                                                            Confirm Deletion
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p class="mb-0">Are you sure you want to delete the teacher <strong><?php echo e($teacher->formal_name); ?></strong>?</p>
                                                        <p class="text-danger small mt-2">
                                                            <i class="bi bi-info-circle-fill me-1"></i>
                                                            This action cannot be undone. All associated data will be permanently deleted.
                                                        </p>
                                                    </div>
                                                    <div class="modal-footer border-0 d-flex justify-content-end gap-2">
                                                        <button type="button" class="btn btn-outline-secondary btn-action" data-bs-dismiss="modal">
                                                            <i class="bi bi-x-circle me-2"></i>
                                                            Cancel
                                                        </button>
                                                        <form action="<?php echo e(route('admin.teachers.destroy', $teacher->id)); ?>" method="POST" class="d-inline">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-danger btn-action">
                                                                <i class="bi bi-trash me-2"></i>
                                                                Delete Teacher
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="bi bi-person-workspace fs-2 d-block mb-2"></i>
                                            No teachers found
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Section -->
                <div class="row mt-4 pt-3 border-top">
                    <div class="col-md-6 col-12 mb-2 mb-md-0">
                        <div class="text-muted small d-flex align-items-center">
                            <i class="bi bi-info-circle me-2"></i>
                            Showing <?php echo e($teachers->firstItem() ?? 0); ?> to <?php echo e($teachers->lastItem() ?? 0); ?> of
                            <?php echo e($teachers->total() ?? 0); ?> entries
                        </div>
                    </div>
                    <div class="col-md-6 col-12">
                        <div class="d-flex justify-content-md-end justify-content-center align-items-center gap-2">
                            <?php if($teachers->hasPages()): ?>
                                <?php if($teachers->onFirstPage()): ?>
                                    <button class="btn btn-outline-secondary btn-action pagination-btn" disabled>
                                        <i class="bi bi-chevron-left me-2"></i>
                                        Previous
                                    </button>
                                <?php else: ?>
                                    <a href="<?php echo e($teachers->previousPageUrl()); ?>" class="btn btn-outline-secondary btn-action pagination-btn">
                                        <i class="bi bi-chevron-left me-2"></i>
                                        Previous
                                    </a>
                                <?php endif; ?>

                                <!-- Page Info -->
                                <span class="px-3 py-2 text-muted small">
                                    Page <?php echo e($teachers->currentPage()); ?> of <?php echo e($teachers->lastPage()); ?>

                                </span>

                                <?php if($teachers->hasMorePages()): ?>
                                    <a href="<?php echo e($teachers->nextPageUrl()); ?>" class="btn btn-primary btn-action pagination-btn">
                                        Next
                                        <i class="bi bi-chevron-right ms-2"></i>
                                    </a>
                                <?php else: ?>
                                    <button class="btn btn-outline-secondary btn-action pagination-btn" disabled>
                                        Next
                                        <i class="bi bi-chevron-right ms-2"></i>
                                    </button>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSV Upload Modal -->
    <div class="modal fade" id="uploadCsvModal" tabindex="-1" aria-labelledby="uploadCsvModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadCsvModalLabel">Import Teachers from CSV</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="<?php echo e(route('admin.teachers.import')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="csvFile" class="form-label">Select CSV File</label>
                            <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                            <div class="form-text">
                                The CSV file should have the following columns:
                                <ul class="mb-0 mt-1">
                                    <li><strong>Required:</strong> last_name, first_name, email, gender</li>
                                    <li><strong>Optional:</strong> middle_name, suffix, street_address, barangay, municipality, province, phone, birthdate</li>
                                </ul>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Note:</strong>
                            <ul class="mb-0 mt-1">
                                <li>Make sure your CSV file follows the required format</li>
                                <li>Dates must be in MM/DD/YYYY format</li>
                                <li>Gender must be one of: Male, Female, Other</li>
                                <li>Email addresses must be valid</li>
                            </ul>
                        </div>
                        <a href="<?php echo e(route('admin.teachers.template')); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-download me-2"></i>
                            Download Template
                        </a>
                    </div>
                    <div class="modal-footer border-0 d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-outline-secondary btn-action" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-success btn-action">
                            <i class="bi bi-upload me-2"></i>
                            Import Teachers
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Button Styling - Same size as student page */
        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 10px 16px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 130px;
            height: 42px;
            justify-content: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .btn-success.btn-action {
            background: linear-gradient(135deg, #10b981, #34d399);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn-success.btn-action:hover {
            background: linear-gradient(135deg, #059669, #10b981);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-danger.btn-action {
            background: linear-gradient(135deg, #dc2626, #ef4444);
            color: white;
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
        }

        .btn-danger.btn-action:hover {
            background: linear-gradient(135deg, #b91c1c, #dc2626);
            box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        /* Table Action Buttons */
        .btn-table-action {
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            font-size: 12px;
            transition: var(--transition);
            min-width: 36px;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-table-action:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .pagination-btn {
            padding: 10px 16px;
            font-size: 13px;
            font-weight: 600;
            border-radius: 10px;
            min-width: 120px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: var(--transition);
            border: 2px solid;
        }

        .pagination-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .pagination-btn:disabled {
            opacity: 0.65;
            cursor: not-allowed;
            transform: none !important;
        }

        .card.shadow-sm {
            background-color: #f8f9fa;
        }

        .avatar-sm {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .avatar-sm i {
            font-size: 1rem;
        }

        .table th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
        }

        .table td {
            vertical-align: middle;
        }

        .badge {
            padding: 0.5em 0.75em;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 0.5rem;
        }

        .alert-success {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #842029;
        }

        /* Enhanced Search Container */
        .search-container {
            position: relative;
            width: 320px;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 4px;
            height: 42px;
            transition: var(--transition);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .search-input-wrapper:hover {
            border-color: #cbd5e0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-input-wrapper:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .search-icon {
            color: #64748b;
            font-size: 16px;
            margin-left: 12px;
            margin-right: 8px;
            transition: var(--transition);
            z-index: 2;
        }

        .search-input-wrapper:focus-within .search-icon {
            color: var(--primary);
            transform: scale(1.1);
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            padding: 10px 8px;
            font-size: 14px;
            font-weight: 500;
            color: #1e293b;
            placeholder-color: #94a3b8;
            height: 100%;
        }

        .search-input::placeholder {
            color: #94a3b8;
            font-weight: 400;
        }

        .search-loading-spinner {
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-clear-btn {
            background: none;
            border: none;
            color: #64748b;
            font-size: 16px;
            margin-right: 8px;
            padding: 4px;
            border-radius: 50%;
            transition: var(--transition);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn:hover {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

        /* Search Suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            z-index: 1000;
            margin-top: 4px;
            max-height: 300px;
            overflow-y: auto;
            backdrop-filter: blur(8px);
        }

        .search-suggestion-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .search-suggestion-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(129, 140, 248, 0.02));
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .search-suggestion-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .search-suggestion-content {
            flex: 1;
        }

        .search-suggestion-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 14px;
            margin-bottom: 2px;
        }

        .search-suggestion-subtitle {
            color: #64748b;
            font-size: 12px;
        }



        /* Search Status Indicator */
        .search-status {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }

        .modal-header {
            padding: 1.5rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
        }

        .modal-title {
            font-weight: 600;
            color: #2c3e50;
        }

        /* Force dropdown positioning - Align with button */
        .dropdown.show #teacherFilterDropdown + .dropdown-menu {
            display: block !important;
            position: absolute !important;
            top: calc(100% + 8px) !important;
            left: 0 !important;
            transform: translateX(0) !important;
            min-width: 150px;
            max-width: 180px;
            width: auto;
        }

        /* Enhanced Filter Dropdown Styling */
        #teacherFilterDropdown + .dropdown-menu {
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            padding: 12px;
            margin-top: 8px !important;
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.98);
            min-width: 150px;
            max-width: 180px;
            width: auto;
            transform-origin: top left;
            transition: opacity 0.15s ease, transform 0.15s ease;
            position: absolute !important;
            top: calc(100% + 4px) !important;
            left: 0 !important;
            right: auto !important;
            bottom: auto !important;
            z-index: 1050;
            will-change: transform, opacity;
            transform: translateX(0) !important;
        }

        /* Filter Dropdown Arrow Pointer - Aligned to right side of button */
        #teacherFilterDropdown + .dropdown-menu::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 110px; /* Adjusted for smaller dropdown width to align with right side of button */
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.98);
            transform: rotate(45deg);
            border-radius: 2px;
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        #teacherFilterDropdown + .dropdown-menu.show {
            animation: dropdownFadeIn 0.2s ease-out forwards;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .dropdown-item {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 4px;
            transition: var(--transition);
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .dropdown-item:last-child {
            margin-bottom: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: var(--primary-dark);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }

        .dropdown-item.active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            transform: translateX(1px);
        }

        .dropdown-item.active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            color: white;
            transform: translateX(2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
        }

        .dropdown-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            transition: var(--transition);
        }

        .dropdown-item:hover i {
            transform: scale(1.05);
        }

        .dropdown-item.active i {
            color: white;
        }

        .dropdown-divider {
            margin: 8px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            opacity: 1;
        }

        /* Enhanced Dropdown Toggle Button */
        .dropdown-toggle::after {
            margin-left: 8px;
            transition: var(--transition);
        }

        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        /* Filter Active State */
        .btn-secondary.btn-action.dropdown-toggle.filter-active {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            border-color: var(--primary-dark);
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
        }

        .btn-secondary.btn-action.dropdown-toggle.filter-active i {
            color: white;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .btn-action {
                padding: 8px 12px;
                font-size: 13px;
                min-width: 110px;
                height: 38px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .btn-table-action {
                padding: 6px 8px;
                min-width: 32px;
                height: 32px;
                font-size: 11px;
            }

            .pagination-btn {
                padding: 8px 12px;
                font-size: 12px;
                min-width: auto;
            }

            /* Enhanced Search Mobile Responsive */
            .search-container {
                width: 100%;
                max-width: 280px;
            }

            .search-input-wrapper {
                border-radius: 10px;
                padding: 2px;
                height: 38px;
            }

            .search-input {
                padding: 8px 6px;
                font-size: 13px;
                height: 100%;
            }

            .search-input::placeholder {
                font-size: 13px;
            }

            .search-icon {
                font-size: 14px;
                margin-left: 10px;
                margin-right: 6px;
            }

            .search-clear-btn {
                font-size: 14px;
                margin-right: 6px;
            }

            .search-suggestions {
                border-radius: 10px;
                max-height: 250px;
            }

            .search-suggestion-item {
                padding: 10px 12px;
            }

            .search-suggestion-icon {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .search-suggestion-title {
                font-size: 13px;
            }

            .search-suggestion-subtitle {
                font-size: 11px;
            }
        }

        @media (max-width: 576px) {
            .search-container {
                max-width: 240px;
            }

            .search-input::placeholder {
                content: "Search teachers...";
            }

            /* Enhanced Filter Dropdown Mobile Responsive */
            #teacherFilterDropdown + .dropdown-menu {
                border-radius: 12px;
                padding: 8px;
                min-width: 140px;
                max-width: 170px;
                width: auto;
                box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            }

            .dropdown-item {
                padding: 10px 12px;
                border-radius: 8px;
                font-size: 13px;
            }

            .dropdown-item i {
                width: 18px;
                height: 18px;
                font-size: 12px;
                margin-right: 10px;
            }

            .dropdown-toggle::after {
                margin-left: 6px;
            }

            /* Enhanced Filter Dropdown Mobile Responsive Arrow */
            #teacherFilterDropdown + .dropdown-menu::before {
                left: 90px; /* Adjusted for smaller mobile dropdown to align with right side of button */
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const searchInput = document.querySelector('.search-input');
            const searchContainer = document.querySelector('.search-container');
            const loadingSpinner = document.querySelector('.search-loading-spinner');
            const clearBtn = document.querySelector('.search-clear-btn');
            const searchSuggestions = document.querySelector('.search-suggestions');

            const searchTimeout = 150;
            let timeoutId;
            let currentRequest = null;

            // Show/hide clear button based on input value
            function toggleClearButton() {
                if (searchInput.value.trim()) {
                    clearBtn.style.display = 'flex';
                } else {
                    clearBtn.style.display = 'none';
                }
            }

            // Show loading spinner
            function showLoading() {
                loadingSpinner.style.display = 'flex';
                clearBtn.style.display = 'none';
            }

            // Hide loading spinner
            function hideLoading() {
                loadingSpinner.style.display = 'none';
                toggleClearButton();
            }

            // Clear search
            clearBtn.addEventListener('click', function() {
                searchInput.value = '';
                searchInput.focus();
                toggleClearButton();

                // Clear search and show all rows
                performRealTimeSearch();
            });

            // Handle input events - Real-time search
            searchInput.addEventListener('input', function () {
                clearTimeout(timeoutId);
                toggleClearButton();

                const searchValue = this.value.trim();

                // Show loading for longer searches
                if (searchValue.length > 2) {
                    showLoading();
                }

                // Perform real-time search with slight delay for better UX
                timeoutId = setTimeout(() => {
                    hideLoading();
                    performRealTimeSearch();
                }, 100); // Much faster response time
            });

            // Real-time search function - shows/hides rows without changing content
            function performRealTimeSearch() {
                const searchTerm = searchInput.value.trim().toLowerCase();
                const tableBody = document.querySelector('tbody');
                const rows = tableBody.querySelectorAll('tr');
                let visibleCount = 0;

                rows.forEach(row => {
                    // Skip the "no teachers found" row
                    if (row.querySelector('td[colspan]')) {
                        return;
                    }

                    const cells = row.querySelectorAll('td');
                    let rowText = '';

                    // Combine all cell text content for searching
                    cells.forEach(cell => {
                        // Skip the actions column (last column)
                        if (cell !== cells[cells.length - 1]) {
                            rowText += cell.textContent.toLowerCase() + ' ';
                        }
                    });

                    // Show/hide row based on search match
                    if (!searchTerm || rowText.includes(searchTerm)) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show "no results" message if no rows are visible
                showNoResultsMessage(visibleCount, searchTerm);

                // Update stats display
                updateSearchStats(visibleCount, searchTerm);
            }

            // Show no results message
            function showNoResultsMessage(visibleCount, searchTerm) {
                const tableBody = document.querySelector('tbody');
                let noResultsRow = tableBody.querySelector('.no-results-row');

                if (visibleCount === 0 && searchTerm) {
                    if (!noResultsRow) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-search fs-1 mb-2 d-block"></i>
                                    <p class="mb-0">No teachers found matching "${searchTerm}"</p>
                                </div>
                            </td>
                        `;
                        tableBody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }

            // Update search statistics
            function updateSearchStats(visibleCount, searchTerm) {
                const statsElement = document.querySelector('.text-muted.small');
                if (statsElement) {
                    if (searchTerm) {
                        statsElement.textContent = `Showing ${visibleCount} teachers matching "${searchTerm}"`;
                    } else {
                        // Reset to original pagination text when no search
                        const totalTeachers = document.querySelectorAll('tbody tr:not(.no-results-row)').length;
                        statsElement.textContent = `Showing ${totalTeachers} teachers`;
                    }
                }
            }

            // Enhanced fetch results function for pagination
            function fetchResults(url) {
                const controller = new AbortController();
                currentRequest = controller;

                fetch(url, {
                    signal: controller.signal,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'text/html'
                    }
                })
                    .then(response => response.text())
                    .then(html => {
                        // Parse the response and update the table
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const newTableBody = doc.querySelector('tbody');
                        const currentTableBody = document.querySelector('tbody');

                        if (newTableBody && currentTableBody) {
                            // Add fade effect
                            currentTableBody.style.opacity = '0.7';
                            setTimeout(() => {
                                currentTableBody.innerHTML = newTableBody.innerHTML;
                                currentTableBody.style.opacity = '1';

                                // Re-attach delete button event listeners
                                attachDeleteButtonListeners();

                                // Apply search filter to new content
                                if (searchInput.value.trim()) {
                                    performRealTimeSearch();
                                }
                            }, 100);
                        }

                        // Update pagination and stats
                        updatePaginationAndStats(doc);
                    })
                    .catch(error => {
                        if (error.name !== 'AbortError') {
                            console.error('Search error:', error);
                            showSearchError();
                        }
                    })
                    .finally(() => {
                        hideLoading();
                        currentRequest = null;
                    });
            }

            // Update pagination and stats
            function updatePaginationAndStats(doc) {
                const newPagination = doc.querySelector('.d-flex.justify-content-between.align-items-center.mt-4');
                const currentPagination = document.querySelector('.d-flex.justify-content-between.align-items-center.mt-4');

                if (newPagination && currentPagination) {
                    currentPagination.innerHTML = newPagination.innerHTML;
                }
            }

            // Show search error
            function showSearchError() {
                const tableBody = document.querySelector('tbody');
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center py-4">
                            <div class="text-danger">
                                <i class="bi bi-exclamation-triangle fs-1 mb-2 d-block"></i>
                                <p class="mb-0">Search failed. Please try again.</p>
                            </div>
                        </td>
                    </tr>
                `;
            }

            // Re-attach delete button event listeners after AJAX update
            function attachDeleteButtonListeners() {
                // Re-attach modal delete functionality
                document.querySelectorAll('[data-bs-target^="#deleteModal"]').forEach(button => {
                    button.addEventListener('click', function() {
                        // Modal functionality is handled by Bootstrap, no additional JS needed
                    });
                });
            }

            // Initialize
            toggleClearButton();

            // Fix dropdown positioning - Align with button
            const dropdownToggle = document.getElementById('teacherFilterDropdown');
            if (dropdownToggle) {
                dropdownToggle.addEventListener('shown.bs.dropdown', function () {
                    const dropdownMenu = this.nextElementSibling;
                    if (dropdownMenu) {
                        // Get button position and dimensions
                        const buttonRect = this.getBoundingClientRect();
                        const containerRect = this.closest('.container').getBoundingClientRect();

                        // Calculate position relative to container
                        const leftOffset = buttonRect.left - containerRect.left;

                        // Set dropdown position to align with button
                        dropdownMenu.style.left = leftOffset + 'px';
                        dropdownMenu.style.top = (buttonRect.bottom - containerRect.top + 8) + 'px';
                        dropdownMenu.style.position = 'absolute';
                        dropdownMenu.style.zIndex = '1050';
                    }
                });
            }
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\MedellinNHS\finalNani\Capstone\resources\views/admin/teachers/index.blade.php ENDPATH**/ ?>