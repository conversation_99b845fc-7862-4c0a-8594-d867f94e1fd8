@extends('layouts.app')

@section('content')
<div class="container-fluid py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">Edit Learning Area</h2>
            <p class="text-muted mb-0 small">Update learning area information</p>
        </div>
    </div>

    <!-- Form Card -->
    <div class="card shadow-lg border-0">
        <div class="card-body p-4">
            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-circle-fill me-2"></i>
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <form action="{{ route('admin.subjects.update', $subject->id) }}" method="POST">
                @csrf
                @method('PUT')
                
                <!-- Basic Information -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5 class="mb-3">Basic Information</h5>
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Learning Area Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                id="name" name="name" value="{{ old('name', $subject->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="code" class="form-label">Learning Area Code</label>
                            <input type="text" class="form-control bg-light" 
                                id="code" value="{{ $subject->code }}" readonly>
                            <div class="form-text">Learning area codes cannot be modified</div>
                        </div>

                        <input type="hidden" name="code" value="{{ $subject->code }}">
                    </div>
                    
                    <div class="col-md-6">
                        <h5 class="mb-3">Additional Information</h5>
                        
                        <div class="mb-3">
                            <label for="teacher_id" class="form-label">Assign Teacher</label>
                            <select class="form-select @error('teacher_id') is-invalid @enderror" id="teacher_id" name="teacher_id">
                                <option value="">Select a teacher</option>
                                @foreach($teachers as $teacher)
                                    <option value="{{ $teacher->id }}" {{ old('teacher_id', $subject->teacher_id) == $teacher->id ? 'selected' : '' }}>
                                        {{ $teacher->formal_name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('teacher_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="section_id" class="form-label">Assign Class Section</label>
                            <select class="form-select @error('section_id') is-invalid @enderror" id="section_id" name="section_id">
                                <option value="">Select a class section</option>
                                @foreach($sections as $section)
                                    <option value="{{ $section->id }}" {{ old('section_id', $subject->section_id) == $section->id ? 'selected' : '' }}>
                                        {{ $section->name }}
                                    </option>
                                @endforeach
                            </select>
                            @if($sections->isEmpty())
                                <div class="form-text text-warning">
                                    <i class="bi bi-exclamation-triangle me-1"></i>
                                    No class sections available for Grade {{ $subject->grade_level }}
                                </div>
                            @endif
                            @error('section_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Schedule Information -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3">Schedule Information</h5>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Update the daily schedule for this learning area.
                        </div>
                        
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Start Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                                name="start_time" value="{{ old('start_time', $startTime) }}" required>
                                            @error('start_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">End Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control @error('end_time') is-invalid @enderror" 
                                                name="end_time" value="{{ old('end_time', $endTime) }}" required>
                                            @error('end_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end gap-2 mt-4">
                    @if($subject->parent_id)
                        <a href="{{ route('admin.subjects.label.subjects', $subject->parent_id) }}" class="btn btn-secondary btn-action">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                    @else
                        <a href="{{ route('admin.subjects.index') }}" class="btn btn-secondary btn-action">
                            <i class="bi bi-x-circle me-2"></i>
                            Cancel
                        </a>
                    @endif
                    <button type="submit" class="btn btn-primary btn-action">
                        <i class="bi bi-check-circle me-2"></i>
                        Update Learning Area
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* CSS Variables */
:root {
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Button Styling to Match Create Page */
.btn-action {
    display: inline-flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.btn-primary.btn-action {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary.btn-action:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    transform: translateY(-2px);
    color: white;
}

.btn-secondary.btn-action {
    background: #ffffff;
    color: #64748b;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary.btn-action:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #374151;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.card {
    border-radius: 0.5rem;
}

.form-label {
    font-weight: 500;
}

.schedule-item {
    background-color: #f8f9fa;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .btn-action {
        padding: 10px 16px;
        font-size: 13px;
        min-width: auto;
    }
}
</style>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('schedules-container');
    const form = document.querySelector('form');

    // Validate time conflicts
    const validateTimeConflicts = () => {
        const schedules = container.querySelectorAll('.schedule-item');
        const conflicts = [];

        schedules.forEach((schedule, i) => {
            const day = schedule.querySelector(`select[name="schedules[${i}][day]"]`).value;
            const startTime = schedule.querySelector(`input[name="schedules[${i}][start_time]"]`).value;
            const endTime = schedule.querySelector(`input[name="schedules[${i}][end_time]"]`).value;

            if (day && startTime && endTime) {
                schedules.forEach((otherSchedule, j) => {
                    if (i !== j) {
                        const otherDay = otherSchedule.querySelector(`select[name="schedules[${j}][day]"]`).value;
                        const otherStartTime = otherSchedule.querySelector(`input[name="schedules[${j}][start_time]"]`).value;
                        const otherEndTime = otherSchedule.querySelector(`input[name="schedules[${j}][end_time]"]`).value;

                        if (day === otherDay && 
                            ((startTime >= otherStartTime && startTime < otherEndTime) ||
                             (endTime > otherStartTime && endTime <= otherEndTime) ||
                             (startTime <= otherStartTime && endTime >= otherEndTime))) {
                            conflicts.push(`Schedule ${i + 1} conflicts with Schedule ${j + 1}`);
                        }
                    }
                });
            }
        });

        return conflicts;
    };

    // Add validation before form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            const conflicts = validateTimeConflicts();
            if (conflicts.length > 0) {
                e.preventDefault();
                alert('Schedule conflicts detected:\n' + conflicts.join('\n'));
            }
        });
    }
});
</script>
@endpush
@endsection 