@extends('layouts.app')

@section('content')
<div class="container-fluid py-3">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">{{ $subjectLabel->name }}</h2>
            <p class="text-muted mb-0 small">Manage learning areas under this category</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.subjects.index') }}" class="btn btn-secondary btn-action">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Learning Areas
            </a>
            <a href="{{ route('admin.subjects.create', ['parent_id' => $subjectLabel->id, 'grade_level' => $subjectLabel->grade_level]) }}"
               class="btn btn-primary btn-action">
                <i class="bi bi-plus-lg me-2"></i>
                Add Learning Area
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="card shadow-lg border-0">
        <div class="card-body p-4">
            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="bg-light">
                        <tr>
                            <th class="text-primary">Name</th>
                            <th class="text-primary">Code</th>
                            <th class="text-primary">Teacher</th>
                            <th class="text-primary">Class Section</th>
                            <th class="text-primary">Schedule</th>
                            <th class="text-primary text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($subjects as $subject)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                            <i class="bi bi-book text-info"></i>
                                        </div>
                                        <div>
                                            <div class="fw-medium">{{ $subject->name }}</div>
                                            @if($subject->description)
                                                <div class="small text-muted">{{ Str::limit($subject->description, 50) }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-light text-dark">{{ $subject->code }}</span>
                                </td>
                                <td>
                                    @if($subject->teacher)
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <div>{{ $subject->teacher->formal_name }}</div>
                                        </div>
                                    @else
                                        <span class="text-muted">Not assigned</span>
                                    @endif
                                </td>
                                <td>
                                    @if($subject->section)
                                        <span class="badge bg-success bg-opacity-10 text-success">
                                            <i class="bi bi-bookmark me-1"></i>
                                            {{ $subject->section->name }} (Grade {{ $subject->section->grade_level }})
                                        </span>
                                    @else
                                        <span class="text-muted">Not assigned</span>
                                    @endif
                                </td>
                                <td>
                                    @if($subject->schedules->isNotEmpty())
                                        @php
                                            $schedule = $subject->schedules->first();
                                            $allDaysSame = $subject->schedules->every(function ($schedule) use ($subject) {
                                                return $schedule->start_time === $subject->schedules->first()->start_time 
                                                    && $schedule->end_time === $subject->schedules->first()->end_time;
                                            });
                                        @endphp
                                        <div class="small">
                                            <div>{{ $allDaysSame ? 'Everyday' : $schedule->day }}</div>
                                            <div class="text-muted">
                                                {{ \Carbon\Carbon::parse($schedule->start_time)->format('h:i A') }} - 
                                                {{ \Carbon\Carbon::parse($schedule->end_time)->format('h:i A') }}
                                            </div>
                                        </div>
                                    @else
                                        <span class="text-muted">No schedule set</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex justify-content-end gap-2">
                                        <a href="{{ route('admin.subjects.edit', $subject) }}"
                                           class="btn btn-sm btn-outline-primary btn-table-action" title="Edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form action="{{ route('admin.subjects.destroy', $subject) }}"
                                              method="POST"
                                              class="d-inline"
                                              onsubmit="return confirm('Are you sure you want to remove this learning area?');">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger btn-table-action" title="Remove">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-inbox-fill fs-2 d-block mb-2"></i>
                                        No learning areas found
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
/* CSS Variables */
:root {
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Button Styling to Match Admin Students/Teachers */
.btn-action {
    display: inline-flex;
    align-items: center;
    padding: 10px 16px;
    border-radius: 10px;
    font-weight: 500;
    font-size: 14px;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 130px;
    height: 42px;
    box-sizing: border-box;
    justify-content: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.btn-primary.btn-action {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary.btn-action:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    transform: translateY(-2px);
    color: white;
}

.btn-secondary.btn-action {
    background: #ffffff;
    color: #64748b;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary.btn-action:hover {
    background: #f8fafc;
    border-color: #cbd5e0;
    color: #374151;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.btn-danger.btn-action {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.btn-danger.btn-action:hover {
    background: linear-gradient(135deg, #b91c1c, #dc2626);
    box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    transform: translateY(-2px);
    color: white;
}

/* Table Action Buttons */
.btn-table-action {
    padding: 8px 12px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 12px;
    transition: var(--transition);
    min-width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-table-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.avatar-sm {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-sm i {
    font-size: 1rem;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert i {
    font-size: 1.1rem;
}

.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .btn-action {
        padding: 10px 16px;
        font-size: 13px;
        min-width: auto;
    }

    .btn-table-action {
        padding: 6px 8px;
        min-width: 32px;
        height: 32px;
        font-size: 11px;
    }
}
</style>
@endsection 