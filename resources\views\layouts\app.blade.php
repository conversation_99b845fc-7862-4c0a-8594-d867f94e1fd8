<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Admin Sidebar Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            margin-left: 260px;
            padding: 1.5rem;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
        }
        .nav-link {
            color: #000;
        }
        .nav-link:hover, .nav-link.active {
            background-color: #f1f1f1;
            font-weight: bold;
        }
        .topbar {
            position: fixed;
            top: 24px;
            left: 280px;
            right: 32px;
            z-index: 1050;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            background: #fff;
            border-radius: 1.5rem;
            box-shadow: 0 4px 24px rgba(80,80,160,0.10);
            padding: 0 2rem 0 2rem;
            min-width: 320px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            will-change: left, width, transform;
            /* Temporary visual indicator - remove in production */
            border: 2px solid transparent;
        }
        .main-content {
            margin-top: 96px;
        }
        .topbar-profile {
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
            border-radius: 50px;
            padding: 4px;
        }

        .topbar-profile:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        .topbar-profile img {
            height: 40px;
            width: 40px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #ff6aab;
            background-color: #f8f9fa;
        }
        .topbar-profile .default-avatar {
            height: 40px;
            width: 40px;
            border-radius: 50%;
            border: 2px solid #ff6aab;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }
        .profile-text {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .profile-name {
            font-size: 1rem;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: all 0.3s ease;
        }

        .profile-role {
            color: #888;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }
        /* Profile Dropdown Container Positioning */
        .topbar .dropdown {
            position: relative;
        }

        /* Enhanced Profile Dropdown Styling */
        .dropdown-menu {
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
            padding: 12px;
            margin-top: 8px !important;
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.98);
            min-width: 200px;
            transform-origin: top right;
            transition: opacity 0.15s ease, transform 0.15s ease;
            animation: dropdownFadeIn 0.2s ease-out forwards;
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
            left: auto !important;
            z-index: 1050;
        }

        @keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Dropdown Arrow Pointer - Points to Profile Picture */
        .dropdown-menu-end::before {
            content: '';
            position: absolute;
            top: -6px;
            right: 50px; /* Adjusted to point to center of profile picture (40px width + 10px margin) */
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.98);
            transform: rotate(45deg);
            border-radius: 2px;
            box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.1);
            z-index: -1;
        }

        /* Responsive arrow positioning for compressed profile */
        @media (max-width: 899px) {
            .dropdown-menu-end::before {
                right: 20px; /* Centered over profile picture when text is hidden */
            }
        }

        @media (max-width: 575px) {
            .dropdown-menu-end::before {
                right: 16px; /* Adjusted for smaller profile picture */
            }
        }

        .dropdown-item {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 4px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .dropdown-item:last-child {
            margin-bottom: 0;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(129, 140, 248, 0.05));
            color: #4f46e5;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
        }

        .dropdown-item:active, .dropdown-item:focus {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.15), rgba(129, 140, 248, 0.08));
            color: #4f46e5;
        }

        .dropdown-item.text-danger:hover {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(254, 226, 226, 0.5));
            color: #dc2626;
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.15);
        }

        .dropdown-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .dropdown-item:hover i {
            transform: scale(1.05);
        }

        .dropdown-divider {
            margin: 8px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            opacity: 1;
        }

        /* Remove blue border and ensure consistent profile dropdown styling */
        .topbar-profile.dropdown-toggle {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle:focus {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle:active {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }

        .topbar-profile.dropdown-toggle[aria-expanded="true"] {
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
        }
        /* Responsive Styles for Main Content and Topbar */

        /* Large Desktop (1200px and up) */
        @media (min-width: 1200px) {
            .main-content {
                margin-left: 280px;
            }
            .topbar {
                left: 300px;
            }
        }

        /* Desktop (992px to 1199px) - Full profile display */
        @media (min-width: 992px) and (max-width: 1199px) {
            .main-content {
                margin-left: 260px;
            }
            .topbar {
                left: 280px;
            }

            .profile-text {
                display: block;
            }

            .profile-name {
                max-width: 140px;
            }
        }

        /* Medium Desktop (1024px to 1199px) - Slightly compressed */
        @media (min-width: 1024px) and (max-width: 1199px) {
            .profile-name {
                max-width: 130px;
                font-size: 0.95rem;
            }

            .profile-role {
                font-size: 0.8rem;
            }
        }

        /* Transition breakpoint (900px to 991px) - Start compressing */
        @media (min-width: 900px) and (max-width: 991px) {
            .profile-name {
                max-width: 100px;
                font-size: 0.9rem;
            }

            .profile-role {
                font-size: 0.75rem;
            }
        }

        /* Mobile layout styles */
        .sidebar-mobile .main-content {
            margin-left: 0;
            padding: 1rem;
            margin-top: 160px;
        }

        .sidebar-mobile .topbar {
            left: 20px;
            right: 20px;
            top: 80px;
            padding: 0 1rem;
            border-radius: 1rem;
            min-width: 80px;
            width: calc(100vw - 40px);
        }

        /* Tablet and Mobile (below 900px) - Profile picture only */
        @media (max-width: 899px) {
            .main-content {
                margin-left: 0;
                padding: 1rem;
                margin-top: 160px;
            }

            .topbar {
                left: 20px !important;
                right: 20px !important;
                top: 80px;
                padding: 0 1rem;
                border-radius: 1rem;
                min-width: 80px;
            }

            /* Hide profile text completely */
            .profile-text {
                display: none !important;
            }

            .topbar-profile {
                gap: 0;
            }
        }

        /* Large Tablet (768px to 991px) - Show only profile picture */
        @media (min-width: 768px) and (max-width: 991px) {
            .profile-text {
                display: none;
            }

            .topbar-profile {
                gap: 0;
            }

            .topbar {
                min-width: 80px;
                padding: 0 1rem;
            }
        }

        /* Mobile (below 768px) - Show only profile picture */
        @media (max-width: 767px) {
            .main-content {
                padding: 0.75rem !important;
                margin-top: 150px !important;
                margin-left: 0 !important;
            }

            .topbar {
                left: 15px !important;
                right: 15px !important;
                top: 75px;
                height: 56px;
                padding: 0 0.75rem;
                min-width: 70px;
            }

            .profile-text {
                display: none;
            }

            .topbar-profile {
                gap: 0;
            }

            .topbar-profile img,
            .topbar-profile .default-avatar {
                height: 36px;
                width: 36px;
            }
        }

        /* Small Mobile (below 576px) - Show only profile picture */
        @media (max-width: 575px) {
            .main-content {
                padding: 0.5rem !important;
                margin-top: 140px !important;
                margin-left: 0 !important;
            }

            .topbar {
                left: 10px !important;
                right: 10px !important;
                top: 70px;
                height: 52px;
                padding: 0 0.5rem;
                min-width: 60px;
            }

            .profile-text {
                display: none;
            }

            .topbar-profile {
                gap: 0;
            }

            .topbar-profile img,
            .topbar-profile .default-avatar {
                height: 32px;
                width: 32px;
                font-size: 14px;
            }
        }

        /* Sidebar state responsive adjustments for desktop */
        @media (min-width: 992px) {
            /* When sidebar is collapsed - topbar expands to fill more space */
            .sidebar-collapsed .main-content {
                margin-left: 70px;
            }

            .sidebar-collapsed .topbar {
                left: 90px !important;
                right: 32px !important;
                width: calc(100vw - 90px - 32px) !important;
                max-width: none !important;
                background: #fff;
                box-shadow: 0 4px 24px rgba(80,80,160,0.10);
            }

            /* When sidebar is expanded - topbar adjusts to available space */
            .sidebar-expanded .main-content {
                margin-left: 260px;
            }

            .sidebar-expanded .topbar {
                left: 280px !important;
                right: 32px !important;
                width: calc(100vw - 280px - 32px) !important;
                max-width: none !important;
                background: #fff;
                box-shadow: 0 4px 24px rgba(80,80,160,0.10);
            }
        }

        /* Large Desktop specific adjustments */
        @media (min-width: 1200px) {
            .sidebar-expanded .main-content {
                margin-left: 280px;
            }

            .sidebar-expanded .topbar {
                left: 300px !important;
                right: 32px !important;
                width: calc(100vw - 300px - 32px) !important;
                background: #fff;
            }

            .sidebar-collapsed .topbar {
                left: 90px !important;
                width: calc(100vw - 90px - 32px) !important;
                background: #fff;
            }
        }

        /* Desktop (992px to 1199px) specific adjustments */
        @media (min-width: 992px) and (max-width: 1199px) {
            .sidebar-expanded .main-content {
                margin-left: 260px;
            }

            .sidebar-expanded .topbar {
                left: 280px !important;
                right: 32px !important;
                width: calc(100vw - 280px - 32px) !important;
                background: #fff;
            }

            .sidebar-collapsed .topbar {
                left: 90px !important;
                width: calc(100vw - 90px - 32px) !important;
                background: #fff;
            }
        }

        /* Animation for smooth transitions */
        @media (prefers-reduced-motion: no-preference) {
            .main-content,
            .topbar {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
        }
    </style>
</head>
<body>

    @include('layouts.sidebar')

    <div class="main-wrapper">
        <!-- Topbar -->
        <div class="topbar">
            <!-- Temporary debug indicator - remove in production -->
            <div id="topbarDebug" style="position: absolute; top: -25px; left: 10px; background: rgba(111, 78, 242, 0.9); color: white; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-family: monospace; z-index: 1000; display: none;">
                Width: <span id="debugWidth">0px</span> | State: <span id="debugState">unknown</span>
            </div>
            <div class="dropdown">
                <a href="#" class="d-flex align-items-center text-decoration-none text-dark topbar-profile dropdown-toggle" id="profileDropdown" data-bs-toggle="dropdown" aria-expanded="false" title="{{ Auth::user()->first_name }} {{ Auth::user()->last_name }} ({{ ucfirst(Auth::user()->role) }})">
                    @if(Auth::user()->hasProfilePicture())
                        <img src="{{ Auth::user()->profile_picture_url }}" alt="User">
                    @else
                        <div class="default-avatar">
                            {{ strtoupper(substr(Auth::user()->first_name, 0, 1)) }}{{ strtoupper(substr(Auth::user()->last_name, 0, 1)) }}
                        </div>
                    @endif
                    <div class="ms-2 profile-text">
                        <span class="fw-bold profile-name" title="{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</span>
                        <small class="d-block text-muted profile-role">{{ ucfirst(Auth::user()->role) }}</small>
                    </div>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileDropdown">
                    <li>
                        <a class="dropdown-item d-flex align-items-center" href="{{ route('admin.profile') }}">
                            <i class="bi bi-person me-2"></i> My Profile
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form action="{{ route('logout') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="dropdown-item d-flex align-items-center text-danger">
                                <i class="bi bi-box-arrow-right me-2"></i> Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>

        <div class="main-content">
            <div class="container-fluid">
                @yield('content')
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Fix profile dropdown positioning
            const profileDropdown = document.getElementById('profileDropdown');
            if (profileDropdown) {
                profileDropdown.addEventListener('shown.bs.dropdown', function () {
                    const dropdownMenu = this.nextElementSibling;
                    if (dropdownMenu) {
                        dropdownMenu.style.position = 'absolute';
                        dropdownMenu.style.top = '100%';
                        dropdownMenu.style.right = '0';
                        dropdownMenu.style.left = 'auto';
                        dropdownMenu.style.transform = 'translateY(8px)';
                        dropdownMenu.style.marginTop = '8px';
                    }
                });
            }

            // Handle sidebar state changes for responsive layout
            const sidebar = document.getElementById('adminSidebar');
            const mainContent = document.querySelector('.main-content');
            const topbar = document.querySelector('.topbar');
            const body = document.body;

            // Debug elements
            const debugIndicator = document.getElementById('topbarDebug');
            const debugWidth = document.getElementById('debugWidth');
            const debugState = document.getElementById('debugState');

            // Show debug indicator
            if (debugIndicator) {
                debugIndicator.style.display = 'block';
            }

            if (sidebar && mainContent && topbar) {
                // Function to update layout based on sidebar state
                function updateResponsiveLayout() {
                    const isDesktop = window.innerWidth >= 992;

                    // Remove all sidebar state classes first
                    body.classList.remove('sidebar-collapsed', 'sidebar-expanded', 'sidebar-mobile');

                    if (isDesktop) {
                        const isCollapsed = sidebar.classList.contains('collapsed');

                        if (isCollapsed) {
                            body.classList.add('sidebar-collapsed');
                            console.log('🔄 Topbar: Sidebar COLLAPSED - Expanding topbar to fill space');

                            // Force immediate CSS application for collapsed state
                            topbar.style.left = '90px';
                            topbar.style.right = '32px';
                            topbar.style.width = 'calc(100vw - 90px - 32px)';

                            // Verify the change took effect
                            setTimeout(() => {
                                const rect = topbar.getBoundingClientRect();
                                console.log('✅ Collapsed state applied - Width:', Math.round(rect.width) + 'px');
                                console.log('✅ Left position:', Math.round(rect.left) + 'px');
                            }, 50);

                        } else {
                            body.classList.add('sidebar-expanded');
                            console.log('🔄 Topbar: Sidebar EXPANDED - Adjusting topbar width');

                            // Force immediate CSS application for expanded state
                            const sidebarWidth = window.innerWidth >= 1200 ? 280 : 260;
                            const leftPos = sidebarWidth + 20;

                            topbar.style.left = leftPos + 'px';
                            topbar.style.right = '32px';
                            topbar.style.width = `calc(100vw - ${leftPos}px - 32px)`;

                            // Verify the change took effect
                            setTimeout(() => {
                                const rect = topbar.getBoundingClientRect();
                                console.log('✅ Expanded state applied - Width:', Math.round(rect.width) + 'px');
                                console.log('✅ Left position:', Math.round(rect.left) + 'px');
                            }, 50);
                        }

                    } else {
                        body.classList.add('sidebar-mobile');
                        console.log('📱 Topbar: Mobile layout - full width topbar');

                        // Mobile layout
                        topbar.style.left = '20px';
                        topbar.style.right = '20px';
                        topbar.style.width = 'calc(100vw - 40px)';
                    }

                    // Add enhanced visual feedback
                    topbar.style.transform = 'scale(0.98)';
                    topbar.style.boxShadow = '0 6px 32px rgba(80,80,160,0.25)';
                    topbar.style.borderColor = 'rgba(111, 78, 242, 0.5)';

                    setTimeout(() => {
                        topbar.style.transform = '';
                        topbar.style.boxShadow = '';
                        topbar.style.borderColor = 'transparent';

                        // Update debug indicator
                        if (debugWidth && debugState) {
                            const rect = topbar.getBoundingClientRect();
                            debugWidth.textContent = Math.round(rect.width) + 'px';
                            debugState.textContent = isDesktop ?
                                (sidebar.classList.contains('collapsed') ? 'COLLAPSED' : 'EXPANDED') :
                                'MOBILE';
                        }
                    }, 400);
                }

                // Create a MutationObserver to watch for sidebar class changes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            console.log('🔍 Sidebar class changed:', sidebar.className);
                            console.log('🔍 Contains collapsed:', sidebar.classList.contains('collapsed'));
                            updateResponsiveLayout();
                        }
                    });
                });

                // Start observing sidebar class changes
                observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });

                // Also listen for manual sidebar toggle events
                document.addEventListener('sidebarToggled', function(e) {
                    console.log('🎯 Manual sidebar toggle detected:', e.detail);
                    setTimeout(updateResponsiveLayout, 50);
                });

                // Direct click listener on sidebar toggle button for immediate response
                const sidebarToggleBtn = document.getElementById('sidebarToggle');
                if (sidebarToggleBtn) {
                    sidebarToggleBtn.addEventListener('click', function() {
                        console.log('🖱️ Sidebar toggle button clicked');
                        // Wait a bit for the sidebar class to change, then update
                        setTimeout(updateResponsiveLayout, 100);
                    });
                }

                // Handle window resize
                let resizeTimeout;
                window.addEventListener('resize', function() {
                    clearTimeout(resizeTimeout);
                    resizeTimeout = setTimeout(updateResponsiveLayout, 150);
                });

                // Function to log topbar dimensions for debugging
                function logTopbarDimensions() {
                    const rect = topbar.getBoundingClientRect();
                    const computedStyle = getComputedStyle(topbar);
                    console.log('=== Topbar Dimensions ===');
                    console.log('Width:', rect.width + 'px');
                    console.log('Left position:', rect.left + 'px');
                    console.log('Right position:', (window.innerWidth - rect.right) + 'px from right');
                    console.log('CSS left:', computedStyle.left);
                    console.log('CSS width:', computedStyle.width);
                    console.log('Viewport width:', window.innerWidth + 'px');
                    console.log('Sidebar state:', sidebar.classList.contains('collapsed') ? 'collapsed' : 'expanded');
                    console.log('========================');
                }

                // Add keyboard shortcuts for testing
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                        logTopbarDimensions();
                    }
                    if (e.ctrlKey && e.shiftKey && e.key === 'U') {
                        console.log('🔧 Manual topbar update triggered');
                        updateResponsiveLayout();
                    }
                    if (e.ctrlKey && e.shiftKey && e.key === 'R') {
                        console.log('🔧 Forcing topbar reset');
                        topbar.style.left = '';
                        topbar.style.right = '';
                        topbar.style.width = '';
                        setTimeout(updateResponsiveLayout, 100);
                    }
                    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                        // Toggle debug indicator
                        if (debugIndicator) {
                            debugIndicator.style.display = debugIndicator.style.display === 'none' ? 'block' : 'none';
                            console.log('🐛 Debug indicator toggled');
                        }
                    }
                });

                // Periodic sync check to ensure topbar stays in sync with sidebar
                let lastSidebarState = null;
                function syncCheck() {
                    const currentState = sidebar.classList.contains('collapsed');
                    if (lastSidebarState !== currentState) {
                        console.log('🔄 Sync check: Sidebar state changed to', currentState ? 'COLLAPSED' : 'EXPANDED');
                        lastSidebarState = currentState;
                        updateResponsiveLayout();
                    }
                }

                // Run sync check every 2 seconds
                setInterval(syncCheck, 2000);

                // Initial update
                setTimeout(() => {
                    updateResponsiveLayout();
                    lastSidebarState = sidebar.classList.contains('collapsed');
                    // Log initial dimensions after a short delay
                    setTimeout(logTopbarDimensions, 200);
                }, 100); // Small delay to ensure sidebar is initialized
            }

            // Handle responsive profile tooltip
            const profileDropdown = document.getElementById('profileDropdown');
            let tooltip = null;

            function updateProfileTooltip() {
                const isCompressed = window.innerWidth <= 899;
                const profileText = document.querySelector('.profile-text');

                if (isCompressed && profileText && getComputedStyle(profileText).display === 'none') {
                    // Show tooltip when profile text is hidden
                    if (!tooltip && typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                        tooltip = new bootstrap.Tooltip(profileDropdown, {
                            placement: 'bottom',
                            trigger: 'hover focus'
                        });
                    }
                } else {
                    // Hide tooltip when profile text is visible
                    if (tooltip) {
                        tooltip.dispose();
                        tooltip = null;
                    }
                }
            }

            // Update tooltip on resize
            let tooltipResizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(tooltipResizeTimeout);
                tooltipResizeTimeout = setTimeout(updateProfileTooltip, 150);
            });

            // Initial tooltip setup
            updateProfileTooltip();
        });
    </script>

    @stack('scripts')
</body>
</html>
