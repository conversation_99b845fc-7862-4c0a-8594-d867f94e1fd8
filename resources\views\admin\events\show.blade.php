@extends('layouts.app')

@section('content')
    <div class="event-show-container">
        <!-- <PERSON> Header -->
        <div class="page-header-section">
            <div class="header-content">
                <div class="title-section">
                    <h1 class="page-title">{{ $event->title }}</h1>
                    <p class="page-subtitle">{{ $event->event_date->format('l, F j, Y') }} • {{ $event->start_time->format('g:i A') }}</p>
                    <div class="event-badges">
                        <div class="status-chip status-{{ strtolower($event->status) }}">
                            <i class="bi bi-{{ $event->status === 'Upcoming' ? 'lightning-charge' : ($event->status === 'Completed' ? 'check-circle' : 'x-circle') }}"></i>
                            <span>{{ $event->status }}</span>
                        </div>
                        <div class="audience-chip">
                            <i class="bi bi-people"></i>
                            <span>{{ $event->visibility }}</span>
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <a href="{{ route('admin.events.edit', $event->event_id) }}" class="btn btn-primary btn-action">
                        <i class="bi bi-pencil-fill me-2"></i>
                        Edit Event
                    </a>
                    <a href="{{ route('admin.events.index') }}" class="btn btn-secondary btn-action">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to Events
                    </a>
                </div>
            </div>
        </div>

        <!-- Content Panels -->
        <div class="content-panels">
            <!-- Description Panel -->
            <div class="glass-panel description-panel">
                <div class="panel-header">
                    <div class="header-decoration">
                        <div class="decoration-dot"></div>
                        <div class="decoration-line"></div>
                    </div>
                    <h2 class="panel-title">
                        <i class="bi bi-chat-quote"></i>
                        Event Description
                    </h2>
                </div>
                <div class="panel-content">
                    <div class="description-block {{ !$event->description ? 'no-description' : '' }}">
                        @if($event->description)
                            <div class="quote-mark">"</div>
                            <p class="description-text">
                                {{ $event->description }}
                            </p>
                        @else
                            <div class="no-description-content">
                                <i class="bi bi-info-circle"></i>
                                <p class="no-description-text">No description available for this event</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Event Overview -->
            <div class="glass-panel overview-panel">
                <div class="panel-header">
                    <div class="header-decoration">
                        <div class="decoration-dot"></div>
                        <div class="decoration-line"></div>
                    </div>
                    <h2 class="panel-title">
                        <i class="bi bi-info-circle"></i>
                        Event Overview
                    </h2>
                </div>
                <div class="panel-content">
                    <div class="overview-grid">
                        <div class="overview-item">
                            <div class="overview-icon">
                                <i class="bi bi-calendar-event"></i>
                            </div>
                            <div class="overview-details">
                                <h4>Event Date</h4>
                                <p>{{ $event->event_date->format('l, F j, Y') }}</p>
                                <span class="overview-meta">{{ $event->event_date->diffForHumans() }}</span>
                            </div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-icon">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="overview-details">
                                <h4>Event Time</h4>
                                <p>{{ $event->start_time->format('g:i A') }} - {{ $event->end_time->format('g:i A') }}</p>
                                <span class="overview-meta">{{ $event->start_time->diffInHours($event->end_time) }} hours duration</span>
                            </div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-icon">
                                <i class="bi bi-geo-alt"></i>
                            </div>
                            <div class="overview-details">
                                <h4>Location</h4>
                                <p>{{ $event->location }}</p>
                                <span class="overview-meta">Event venue</span>
                            </div>
                        </div>
                        <div class="overview-item">
                            <div class="overview-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="overview-details">
                                <h4>Organizer</h4>
                                <p>{{ $event->creator->name ?? 'Unknown' }}</p>
                                <span class="overview-meta">Event administrator</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* ===== MODERN UI DESIGN SYSTEM ===== */

        /* CSS Variables */
        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --secondary: #f59e0b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;

            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;

            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;

            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

            --blur: blur(20px);
            --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        /* Global Styles */
        * {
            box-sizing: border-box;
        }

        .event-show-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 40px;
            position: relative;
            overflow-x: hidden;
        }



        /* Page Header Section */
        .page-header-section {
            padding: 0 0 30px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(99, 102, 241, 0.1);
            position: relative;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 30px;
        }

        .title-section {
            flex: 1;
        }

        .page-title {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 16px;
            font-weight: 400;
        }

        .event-badges {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .status-chip, .audience-chip {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid rgba(99, 102, 241, 0.2);
            border-radius: 20px;
            font-weight: 500;
            font-size: 14px;
            color: #374151;
            box-shadow: 0 2px 6px rgba(99, 102, 241, 0.1);
            transition: var(--transition);
        }

        .status-chip:hover, .audience-chip:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }

        .header-actions {
            display: flex;
            gap: 12px;
            flex-shrink: 0;
        }

        .btn-action {
            display: inline-flex;
            align-items: center;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            text-decoration: none;
            transition: var(--transition);
            border: none;
            cursor: pointer;
            min-width: 140px;
            justify-content: center;
        }

        .btn-primary.btn-action {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            color: white;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .btn-primary.btn-action:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary));
            box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
            transform: translateY(-2px);
            color: white;
        }

        .btn-secondary.btn-action {
            background: #ffffff;
            color: #64748b;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .btn-secondary.btn-action:hover {
            background: #f8fafc;
            border-color: #cbd5e0;
            color: #374151;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .status-chip {
            position: relative;
        }

        .chip-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50px;
            opacity: 0;
            transition: var(--transition);
        }

        .status-upcoming .chip-glow {
            background: var(--gradient-success);
        }

        .status-completed .chip-glow {
            background: var(--gradient-primary);
        }

        .status-cancelled .chip-glow {
            background: var(--gradient-secondary);
        }

        .status-chip:hover .chip-glow {
            opacity: 0.3;
        }

        /* Overview Panel */
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .overview-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px;
            border: 1px solid rgba(99, 102, 241, 0.1);
            transition: var(--transition);
        }

        .overview-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .overview-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            flex-shrink: 0;
            box-shadow: 0 4px 8px rgba(99, 102, 241, 0.2);
        }

        .overview-details {
            flex: 1;
        }

        .overview-details h4 {
            font-size: 1rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 6px 0;
        }

        .overview-details p {
            font-size: 1.125rem;
            font-weight: 500;
            color: #374151;
            margin: 0 0 4px 0;
            line-height: 1.4;
        }

        .overview-meta {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 400;
        }



        /* Content Panels */
        .content-panels {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            position: relative;
        }

        /* Glass Panels */
        .glass-panel {
            background: #ffffff;
            border: 1px solid rgba(99, 102, 241, 0.12);
            border-radius: 20px;
            overflow: hidden;
            transition: var(--transition);
            position: relative;
            box-shadow:
                0 4px 6px -1px rgba(0, 0, 0, 0.1),
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 0 0 1px rgba(99, 102, 241, 0.05);
        }

        .glass-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--secondary));
            border-radius: 20px 20px 0 0;
        }

        .glass-panel:hover {
            transform: translateY(-4px);
            box-shadow:
                0 10px 15px -3px rgba(0, 0, 0, 0.1),
                0 4px 6px -2px rgba(0, 0, 0, 0.05),
                0 0 0 1px rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.2);
        }

        .panel-header {
            padding: 28px 28px 20px;
            position: relative;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-bottom: 1px solid rgba(99, 102, 241, 0.08);
        }

        .header-decoration {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 14px;
        }

        .decoration-dot {
            width: 6px;
            height: 6px;
            background: var(--primary);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.3); }
        }

        .decoration-line {
            width: 35px;
            height: 2px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            border-radius: 1px;
        }

        .panel-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-title i {
            font-size: 1.1rem;
            color: var(--primary);
            opacity: 0.8;
        }

        .panel-content {
            padding: 0 28px 28px;
        }

        /* Description Panel */
        .description-block {
            position: relative;
            padding: 28px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px;
            border: 1px solid rgba(99, 102, 241, 0.15);
            box-shadow: inset 0 1px 3px rgba(99, 102, 241, 0.08);
        }

        .quote-mark {
            position: absolute;
            top: -8px;
            left: 18px;
            font-size: 3rem;
            color: rgba(99, 102, 241, 0.25);
            font-family: Georgia, serif;
            line-height: 1;
        }

        .description-text {
            font-size: 1rem;
            line-height: 1.6;
            color: #374151;
            margin: 0;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        /* No Description State */
        .description-block.no-description {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px dashed rgba(99, 102, 241, 0.2);
        }

        .no-description-content {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: center;
            padding: 20px;
        }

        .no-description-content i {
            font-size: 1.25rem;
            color: #94a3b8;
        }

        .no-description-text {
            font-size: 1rem;
            color: #64748b;
            margin: 0;
            font-style: italic;
            font-weight: 400;
        }

        /* Timeline Panel */
        .timeline {
            position: relative;
            padding: 8px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, var(--primary), var(--primary-light), var(--secondary));
            border-radius: 2px;
        }

        .timeline-item {
            position: relative;
            padding-left: 60px;
            margin-bottom: 28px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
        }

        .timeline-marker {
            position: absolute;
            left: 0;
            top: 0;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            transition: var(--transition);
            border: 2px solid white;
        }

        .timeline-item.active .timeline-marker {
            background: linear-gradient(135deg, var(--secondary), var(--warning));
            box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4);
            animation: glow 2s infinite;
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 6px 16px rgba(245, 158, 11, 0.4); }
            50% { box-shadow: 0 8px 20px rgba(245, 158, 11, 0.6); }
        }

        .timeline-content h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1e293b;
            margin: 0 0 8px 0;
        }

        .timeline-content p {
            font-size: 1rem;
            color: #475569;
            margin: 0 0 8px 0;
            font-weight: 500;
        }

        .timeline-meta {
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 400;
        }



        /* Responsive Design */
        @media (min-width: 768px) {
            .event-show-container {
                padding: 50px 50px;
            }

            .content-panels {
                gap: 40px;
            }

            .header-content {
                flex-direction: row;
                align-items: flex-start;
            }

            .overview-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .event-show-container {
                padding: 60px 60px;
            }

            .content-panels {
                gap: 50px;
            }

            .overview-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 24px;
            }
        }

        @media (min-width: 1200px) {
            .event-show-container {
                padding: 80px 80px;
            }

            .overview-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .event-show-container {
                padding: 20px 20px;
            }

            .page-header-section {
                padding: 0 0 20px 0;
                margin-bottom: 20px;
            }

            .header-content {
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }

            .header-actions {
                flex-direction: column;
                gap: 8px;
            }

            .btn-action {
                padding: 10px 16px;
                font-size: 13px;
                min-width: auto;
            }

            .content-panels {
                gap: 20px;
            }

            .panel-header {
                padding: 20px 20px 16px;
            }

            .panel-content {
                padding: 0 20px 20px;
            }

            .panel-title {
                font-size: 1.125rem;
            }

            .description-block {
                padding: 20px;
            }

            .overview-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .overview-item {
                padding: 16px;
            }

            .overview-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }

            .event-badges {
                gap: 8px;
                margin-bottom: 16px;
            }

            .status-chip, .audience-chip {
                padding: 8px 12px;
                font-size: 12px;
            }
        }

        /* Animations */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .glass-panel {
            animation: slideInUp 0.6s ease-out;
        }

        .glass-panel:nth-child(1) { animation-delay: 0.1s; }
        .glass-panel:nth-child(2) { animation-delay: 0.2s; }
        .glass-panel:nth-child(3) { animation-delay: 0.3s; }

        .hero-content {
            animation: fadeIn 1s ease-out;
        }

        .floating-nav {
            animation: slideInUp 0.8s ease-out;
        }

        /* Color Overrides */
        .text-primary {
            color: var(--primary) !important;
        }

        .bg-primary {
            background-color: var(--primary) !important;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--gradient-primary);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gradient-secondary);
        }
    </style>
@endsection