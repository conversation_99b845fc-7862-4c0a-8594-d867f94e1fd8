<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckStudentStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Only check status for students
        if ($user && $user->role === 'student') {
            // Check if student record exists and has an active status
            if (!$user->student || !$this->isStudentActive($user->student->status)) {
                // Log the user out
                Auth::logout();
                
                // Invalidate the session
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                // Redirect to login with error message
                return redirect()->route('login')->withErrors([
                    'access_denied' => 'Your account has been deactivated. Please contact the administration for assistance.'
                ]);
            }
        }

        return $next($request);
    }

    /**
     * Check if student status allows system access
     *
     * @param string|null $status
     * @return bool
     */
    private function isStudentActive(?string $status): bool
    {
        // Only 'active' status allows system access
        // All other statuses (inactive, dropped, transferred, graduated) deny access
        return $status === 'active';
    }
}
