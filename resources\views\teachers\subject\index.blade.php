@extends('layouts.teacherApp')

@section('content')
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="bg-white rounded-4 shadow-sm p-4 mb-4">
        <div class="d-flex justify-content-between align-items-center">
        <div>
                <h2 class="fw-bold mb-1 text-primary">My Subjects</h2>
                <p class="text-muted mb-0">View and manage your assigned subjects</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="text-end">
                    <div class="text-muted small mb-1">Total Subjects</div>
                    <div class="h4 mb-0 fw-bold text-primary">{{ $subjects->count() }}</div>
                </div>
                <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center">
                    <i class="bi bi-journal-bookmark-fill text-primary fs-4"></i>
                </div>
            </div>
      </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if($subjects->isEmpty())
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            No subjects assigned yet.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @else
      <div class="row g-4">
      @foreach($subjects as $subject)
  <div class="col-md-4 col-sm-6">
                    <div class="card border-0 shadow-sm h-100" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#subjectModal{{ $subject->id }}">
                        <div class="card-body p-4">
        <div class="d-flex align-items-center mb-3">
                                <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-3">
                                    <i class="bi bi-journal-bookmark-fill text-primary"></i>
          </div>
          <div>
                                    <h5 class="fw-bold text-primary mb-1">{{ $subject->name }}</h5>
            <div class="d-flex align-items-center gap-2">
                                        <span class="badge bg-info bg-opacity-10 text-info">{{ $subject->code }}</span>
              <span class="badge bg-primary bg-opacity-10 text-primary">
                <i class="bi bi-people-fill me-1"></i>
                {{ $subject->automaticEnrollments->count() }} Students
              </span>
            </div>
          </div>
        </div>

                            <p class="text-muted small mb-3" style="line-height: 1.6; max-height: 4.8em; overflow: hidden;">
          {{ $subject->description }}
                            </p>

                            <div class="d-flex align-items-center text-muted small">
          @if($subject->schedules->count() > 0)
                                    <i class="bi bi-clock me-2 text-primary"></i>
                                    <span>
              Monday-Friday {{ \Carbon\Carbon::parse($subject->schedules->first()->start_time)->format('h:i A') }}
              - {{ \Carbon\Carbon::parse($subject->schedules->first()->end_time)->format('h:i A') }}
            </span>
          @else
                                    <i class="bi bi-info-circle me-2 text-muted"></i>
                                    <span>No schedule yet</span>
          @endif
                            </div>
      </div>
    </div>

    <!-- Subject Modal -->
    <div class="modal fade" id="subjectModal{{ $subject->id }}" tabindex="-1" aria-labelledby="subjectModalLabel{{ $subject->id }}" aria-hidden="true">
      <div class="modal-dialog modal-lg">
                            <div class="modal-content border-0 shadow">
                                <div class="modal-header border-0 bg-light">
            <h5 class="modal-title" id="subjectModalLabel{{ $subject->id }}">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                <i class="bi bi-journal-bookmark-fill text-primary"></i>
                                            </div>
                                            <div>
              {{ $subject->name }} - {{ $subject->code }}
              <span class="badge bg-primary bg-opacity-10 text-primary ms-2">
                <i class="bi bi-people-fill me-1"></i>
                {{ $subject->automaticEnrollments->count() }} Students
              </span>
                                            </div>
                                        </div>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
                                <div class="modal-body p-4">
            <div class="mb-4">
              <h6 class="text-primary mb-3">Subject Details</h6>
                                        <p class="text-muted">{{ $subject->description }}</p>
              @if($subject->schedules->count() > 0)
                <div class="mt-3">
                  <h6 class="text-primary mb-2">Schedule</h6>
                  <div class="d-flex align-items-center">
                    <i class="bi bi-clock me-2 text-primary"></i>
                                                    <span class="text-muted">
                      Monday-Friday {{ \Carbon\Carbon::parse($subject->schedules->first()->start_time)->format('h:i A') }}
                      - {{ \Carbon\Carbon::parse($subject->schedules->first()->end_time)->format('h:i A') }}
                    </span>
                  </div>
                </div>
              @endif
            </div>

            <div class="mt-4">
              <h6 class="text-primary mb-3">Enrolled Students</h6>
              @if($subject->automaticEnrollments->count() > 0)
                <div class="table-responsive">
                                                <table class="table table-hover align-middle">
                                                    <thead class="bg-light">
                                                        <tr>
                                                            <th class="text-primary">Student ID</th>
                                                            <th class="text-primary">Name</th>
                                                            <th class="text-primary">Grade</th>
                                                            <th class="text-primary text-end">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      @foreach($subject->automaticEnrollments as $enrollment)
                        <tr>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle me-2">
                                                                            <i class="bi bi-person-badge text-primary"></i>
                                                                        </div>
                                                                        <span class="fw-medium">{{ $enrollment->student->student_id }}</span>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div class="d-flex align-items-center">
                                                                        <div class="avatar-sm bg-info bg-opacity-10 rounded-circle me-2">
                                                                            <i class="bi bi-person text-info"></i>
                                                                        </div>
                                                                        <span class="fw-medium">{{ $enrollment->student->user->first_name }} {{ $enrollment->student->user->last_name }}</span>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    @php
                                                                        $grade = $enrollment->student->grades->where('subject_id', $subject->id)->first();
                                                                    @endphp
                                                                    <span class="badge bg-{{ $grade && $grade->grade >= 75 ? 'success' : 'danger' }}">
                                                                        {{ $grade ? $grade->grade : 'Not graded' }}
                                                                    </span>
                                                                </td>
                                                                <td class="text-end">
                                                                    <a href="{{ route('teachers.student.grade.index') }}" class="btn btn-xs btn-outline-primary">
                                                                        <i class="bi bi-pencil"></i>
                            </a>
                          </td>
                        </tr>
                      @endforeach
                    </tbody>
                  </table>
                </div>
              @else
                <div class="alert alert-info">
                  <i class="bi bi-info-circle me-2"></i>
                  No students enrolled in this subject yet.
                </div>
              @endif
            </div>
          </div>
                                <div class="modal-footer border-0 bg-light">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                                        <i class="bi bi-x-circle me-1"></i> Close
                                    </button>
            <a href="{{ route('teachers.student.grade.index') }}" class="btn btn-primary">
              <i class="bi bi-pencil-square me-1"></i> Manage Grades
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
@endforeach
      </div>
    @endif
</div>

<!-- Custom Styling -->
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-sm i {
  font-size: 1rem;
}

.avatar-lg {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar-lg i {
    font-size: 1.5rem;
}

.badge {
    padding: 0.5em 0.75em;
    font-weight: 500;
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    min-width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-xs i {
    font-size: 0.75rem;
}

.table th {
  font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
}

.alert {
    border: none;
    border-radius: 0.5rem;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
</style>

<!-- Bootstrap Icons -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
@endsection
