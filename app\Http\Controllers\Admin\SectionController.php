<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Section;
use App\Models\Student;
use App\Models\User;
use App\Services\AutomaticEnrollmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SectionController extends Controller
{
    protected $enrollmentService;

    public function __construct(AutomaticEnrollmentService $enrollmentService)
    {
        $this->enrollmentService = $enrollmentService;
    }
    public function index(Request $request)
    {
        $selectedGrade = $request->input('grade_level');
        $gradeLevels = ['Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12'];

        $sections = Section::with('adviser')
            ->withCount('students')
            ->when($selectedGrade, function ($query) use ($selectedGrade) {
                return $query->where('grade_level', $selectedGrade);
            })
            ->orderBy('grade_level')
            ->orderBy('created_at', 'desc')
            ->orderBy('name')
            ->paginate(10);

        return view('admin.sections.index', compact('sections', 'gradeLevels', 'selectedGrade'));
    }

    public function create()
    {
        // Get teachers who are not already assigned as advisers to any section
        $assignedTeacherIds = Section::whereNotNull('adviser_id')->pluck('adviser_id')->toArray();
        $teachers = User::where('role', 'teacher')
            ->whereNotIn('id', $assignedTeacherIds)
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        $selectedGrade = request('grade_level');
        return view('admin.sections.create', compact('teachers', 'selectedGrade'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'grade_level' => 'required|string|in:Grade 7,Grade 8,Grade 9,Grade 10,Grade 11,Grade 12',
            'description' => 'nullable|string',
            'adviser_id' => [
                'required',
                'exists:users,id',
                function ($attribute, $value, $fail) {
                    // Check if teacher is already assigned to another section
                    $existingSection = Section::where('adviser_id', $value)->first();
                    if ($existingSection) {
                        $teacher = User::find($value);
                        $fail("Teacher {$teacher->last_name}, {$teacher->first_name} is already assigned as adviser to section '{$existingSection->name}' in {$existingSection->grade_level}.");
                    }
                },
            ],
        ]);

        // Check if a section with the same name already exists in this grade level
        $existingSection = Section::where('grade_level', $validated['grade_level'])
            ->where('name', $validated['name'])
            ->first();

        if ($existingSection) {
            return back()
                ->withInput()
                ->withErrors(['name' => 'A section with this name already exists in ' . $validated['grade_level']]);
        }

        $section = Section::create($validated);

        return redirect()
            ->route('admin.sections.index', ['grade_level' => $section->grade_level])
            ->with('success', 'Section created successfully.');
    }

    public function edit(Section $section)
    {
        // Get teachers who are not already assigned as advisers to any section
        // Include the current section's adviser if it exists
        $assignedTeacherIds = Section::whereNotNull('adviser_id')
            ->where('id', '!=', $section->id)
            ->pluck('adviser_id')
            ->toArray();

        $teachers = User::where('role', 'teacher')
            ->whereNotIn('id', $assignedTeacherIds)
            ->orderBy('last_name')
            ->orderBy('first_name')
            ->get();

        return view('admin.sections.edit', compact('section', 'teachers'));
    }

    public function update(Request $request, Section $section)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'grade_level' => 'required|string|in:Grade 7,Grade 8,Grade 9,Grade 10,Grade 11,Grade 12',
            'description' => 'nullable|string',
            'adviser_id' => [
                'nullable',
                'exists:users,id',
                function ($attribute, $value, $fail) use ($section) {
                    if ($value) {
                        // Check if teacher is already assigned to another section (excluding current section)
                        $existingSection = Section::where('adviser_id', $value)
                            ->where('id', '!=', $section->id)
                            ->first();
                        if ($existingSection) {
                            $teacher = User::find($value);
                            $fail("Teacher {$teacher->last_name}, {$teacher->first_name} is already assigned as adviser to section '{$existingSection->name}' in {$existingSection->grade_level}.");
                        }
                    }
                },
            ],
        ]);

        // Store original values for comparison
        $originalName = $section->name;
        $originalGradeLevel = $section->grade_level;

        DB::beginTransaction();
        try {
            // Update the section
            $section->update($request->all());

            // Check if name or grade level changed and update enrollments accordingly
            if ($originalName !== $section->name || $originalGradeLevel !== $section->grade_level) {
                // Update enrollments for all students in this section
                $this->enrollmentService->updateSectionEnrollments($section);
            }

            DB::commit();

            return redirect()->route('admin.sections.index')
                ->with('success', 'Section updated successfully. All related student records have been updated.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Failed to update section: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function destroy(Section $section)
    {
        $section->delete();

        return redirect()->route('admin.sections.index')
            ->with('success', 'Section deleted successfully.');
    }

    public function students(Section $section)
    {
        $students = $section->students()->with(['user', 'grades' => function($query) {
            $query->orderBy('grading_period', 'desc');
        }])->get();
        return view('admin.sections.students', compact('section', 'students'));
    }
}
