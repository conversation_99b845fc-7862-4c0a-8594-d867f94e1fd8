<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Subject;
use App\Models\Event;
use App\Models\Section;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
       
        $stats['users'] = User::count();
        $stats['students'] = User::where('role', 'student')->count();
        $stats['teachers'] = User::where('role', 'teacher')->count();
        $stats['admins'] = User::where('role', 'admin')->count();

        $stats['subject_labels'] = Subject::whereNull('parent_id')->count();
        $stats['subjects'] = Subject::whereNotNull('parent_id')->count();
        
        $stats['sections'] = Section::where('status', 'active')->count();
        
        $stats['events'] = Event::where('status', 'Upcoming')->count();

        // Define all possible student statuses
        $allStatuses = ['active', 'inactive', 'dropped', 'graduated', 'transferred'];

        // Initialize all statuses with 0 count
        $studentStatuses = array_fill_keys($allStatuses, 0);

        // Get actual student status counts
        $actualStatuses = \App\Models\Student::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->status => $item->count];
            });

        // Merge actual counts with initialized array to ensure all statuses show
        $studentStatuses = array_merge($studentStatuses, $actualStatuses->toArray());

        return view('admin.dashboard', compact('stats', 'studentStatuses'));
    }
} 