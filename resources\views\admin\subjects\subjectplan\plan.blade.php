@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold mb-1 text-primary">Academic Program Structure</h2>
            <p class="text-muted mb-0 small">Manage learning areas and their distribution across academic levels</p>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Junior High School Section -->
        <div class="col-12 mb-5">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="bi bi-mortarboard me-2"></i>Junior High School Program
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        @foreach(['Grade 7', 'Grade 8', 'Grade 9', 'Grade 10'] as $grade)
                            <div class="col-md-6 col-lg-3">
                                <div class="card h-100 border-0 shadow-sm hover-card grade-card">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
                                        <h6 class="mb-0 fw-semibold">{{ $grade }}</h6>
                                        <a href="{{ route('admin.subjects.grade', ['grade' => $grade]) }}"
                                           class="btn btn-primary btn-action btn-sm">
                                            <i class="bi bi-gear-fill me-2"></i>
                                            Manage Program
                                        </a>
                                    </div>
                                    <div class="card-body p-3">
                                        @php
                                            $gradeSubjects = $juniorHighSubjects->where('grade_level', $grade)->whereNull('parent_id');
                                        @endphp
                                        @if($gradeSubjects->count() > 0)
                                            <ul class="list-group list-group-flush subject-list">
                                                @foreach($gradeSubjects->take(5) as $subject)
                                                    <li class="list-group-item px-0">
                                                        <div class="fw-medium">{{ $subject->name }}</div>
                                                        @if($subject->description)
                                                            <div class="small text-muted">{{ Str::limit($subject->description, 50) }}</div>
                                                        @endif
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <div class="text-center py-3">
                                                <div class="text-muted">
                                                    <i class="bi bi-inbox-fill fs-4 d-block mb-2"></i>
                                                    No learning areas assigned to this level
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Senior High School Section -->
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="bi bi-mortarboard me-2"></i>Senior High School Program
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        @foreach(['Grade 11', 'Grade 12'] as $grade)
                            <div class="col-md-6">
                                <div class="card h-100 border-0 shadow-sm hover-card grade-card">
                                    <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
                                        <h6 class="mb-0 fw-semibold">{{ $grade }}</h6>
                                        <a href="{{ route('admin.subjects.grade', ['grade' => $grade]) }}"
                                           class="btn btn-primary btn-action btn-sm">
                                            <i class="bi bi-gear-fill me-2"></i>
                                            Manage Program
                                        </a>
                                    </div>
                                    <div class="card-body p-3">
                                        @php
                                            $gradeSubjects = $seniorHighSubjects->where('grade_level', $grade)->whereNull('parent_id');
                                        @endphp
                                        @if($gradeSubjects->count() > 0)
                                            <ul class="list-group list-group-flush subject-list">
                                                @foreach($gradeSubjects->take(5) as $subject)
                                                    <li class="list-group-item px-0">
                                                        <div class="fw-medium">{{ $subject->name }}</div>
                                                        @if($subject->description)
                                                            <div class="small text-muted">{{ Str::limit($subject->description, 50) }}</div>
                                                        @endif
                                                    </li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <div class="text-center py-3">
                                                <div class="text-muted">
                                                    <i class="bi bi-inbox-fill fs-4 d-block mb-2"></i>
                                                    No learning areas assigned to this level
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* CSS Variables */
:root {
    --primary: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --junior-gradient: linear-gradient(135deg, #3b82f6, #1d4ed8);
    --senior-gradient: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Button Styling to Match Create Page */
.btn-action {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 13px;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    min-width: 120px;
    justify-content: center;
}

.btn-primary.btn-action {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary.btn-action:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    box-shadow: 0 6px 16px rgba(99, 102, 241, 0.4);
    transform: translateY(-2px);
    color: white;
}

.btn-sm.btn-action {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 100px;
    border-radius: 8px;
}

/* Enhanced Card Styling */
.hover-card {
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.hover-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--junior-gradient);
    opacity: 0;
    transition: var(--transition);
}

.hover-card:hover::before {
    opacity: 1;
}

.hover-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15) !important;
    border-color: rgba(99, 102, 241, 0.2);
}

/* Senior High School Cards */
.col-12:last-child .hover-card::before {
    background: var(--senior-gradient);
}

.card {
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(8px);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #f8fafc, #ffffff);
    border-radius: 16px 16px 0 0;
    padding: 16px 20px;
}

.card-header h5 {
    background: var(--junior-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.card-header h6 {
    color: #1e293b;
    font-weight: 600;
}

/* Senior High School Header Styling */
.col-12:last-child .card-header h5 {
    background: var(--senior-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced List Styling */
.list-group-item {
    border: none;
    padding: 12px 0;
    transition: var(--transition);
}

.list-group-item:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.list-group-item:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(129, 140, 248, 0.02));
    border-radius: 8px;
    padding-left: 8px;
    padding-right: 8px;
}

.list-group-item .fw-medium {
    color: #1e293b;
    font-weight: 600;
}

.list-group-item .text-muted {
    color: #64748b !important;
}

/* Enhanced Grade Cards */
.grade-card {
    height: 320px !important;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
}

.grade-card .card-header {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(129, 140, 248, 0.02));
}

/* Enhanced Subject List */
.subject-list {
    max-height: 220px;
    overflow-y: auto;
    padding-right: 8px;
}

.subject-list::-webkit-scrollbar {
    width: 6px;
}

.subject-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 6px;
}

.subject-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary), var(--primary-light));
    border-radius: 6px;
}

.subject-list::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

/* Enhanced Empty State */
.text-center .text-muted {
    color: #64748b !important;
}

.text-center .bi-inbox-fill {
    color: #cbd5e1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Enhanced Section Headers */
.card-header .bi-mortarboard {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Responsive Design */
@media (max-width: 768px) {
    .btn-action {
        padding: 6px 12px;
        font-size: 12px;
        min-width: auto;
    }

    .card {
        border-radius: 12px;
    }

    .grade-card {
        height: auto !important;
        min-height: 280px;
    }

    .subject-list {
        max-height: 180px;
    }
}

/* Animation for cards on load */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grade-card {
    animation: fadeInUp 0.6s ease-out;
}

.grade-card:nth-child(1) { animation-delay: 0.1s; }
.grade-card:nth-child(2) { animation-delay: 0.2s; }
.grade-card:nth-child(3) { animation-delay: 0.3s; }
.grade-card:nth-child(4) { animation-delay: 0.4s; }
</style>
@endsection 