<!-- resources/views/dashboard.blade.php -->
@extends('layouts.app')

@section('content')
    <div class="container-fluid py-4">
        <!-- Welcome Banner -->
        <div class="welcome-banner mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="display-6 fw-bold text-primary mb-2">Welcome back, {{ Auth::user()->name }}! 👋</h1>
                            <p class="text-muted mb-0">Here's an overview of your school's current status.</p>
                        </div>
                        <div class="col-md-4 text-md-end mt-3 mt-md-0">
                            <div class="clock-display">
                                <div class="time" id="current-time">00:00:00</div>
                                <div class="date" id="current-date">Loading...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Left Column: Analytics & Actions -->
            <div class="col-lg-8">
                <div class="dashboard-left-panel h-100 d-flex flex-column">
                    <!-- Student Analytics Chart -->
                    <div class="card border-0 shadow-sm mb-3 flex-grow-1">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="fw-semibold mb-0">Student Status Distribution</h6>
                                <small class="text-muted">Percentage & Total Count</small>
                            </div>
                            <div class="chart-container" style="position: relative; height: 280px;">
                                <canvas id="studentStatusChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Administrative Actions -->
                    <div class="card border-0 shadow-sm admin-actions-panel">
                        <div class="card-body p-2">
                            <h6 class="fw-semibold mb-2 small">Administrative Actions</h6>
                            <div class="row g-1">
                                <div class="col-md-4">
                                    <a href="{{ route('admin.students.create') }}" class="admin-action-btn">
                                        <div class="action-icon bg-primary bg-opacity-10">
                                            <i class="bi bi-person-plus text-primary"></i>
                                        </div>
                                        <div class="action-label">
                                            <span class="mb-0">Add Student</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="{{ route('admin.sections.create') }}" class="admin-action-btn">
                                        <div class="action-icon bg-success bg-opacity-10">
                                            <i class="bi bi-file-earmark-plus text-success"></i>
                                        </div>
                                        <div class="action-label">
                                            <span class="mb-0">Create Section</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="{{ route('admin.events.create') }}" class="admin-action-btn">
                                        <div class="action-icon bg-warning bg-opacity-10">
                                            <i class="bi bi-calendar-plus text-warning"></i>
                                        </div>
                                        <div class="action-label">
                                            <span class="mb-0">Schedule Event</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Performance Metrics -->
            <div class="col-lg-4">
                <div class="dashboard-right-panel h-100 d-flex flex-column">
                    <!-- Performance Metrics -->
                    <div class="row g-3 flex-grow-1">
                        <div class="col-12">
                            <div class="card metric-card border-0 shadow-sm h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small text-uppercase fw-semibold">System Users</h6>
                                            <h3 class="mb-0 fw-bold">{{ $stats['users'] ?? 0 }}</h3>
                                            <div class="d-flex gap-3 mt-2">
                                                <p class="text-primary mb-0 small">
                                                    <i class="bi bi-person-fill"></i> {{ $stats['students'] ?? 0 }} Students
                                                </p>
                                                <p class="text-success mb-0 small">
                                                    <i class="bi bi-person-badge"></i> {{ $stats['teachers'] ?? 0 }} Teachers
                                                </p>
                                            </div>
                                        </div>
                                        <div class="metric-icon bg-primary bg-opacity-10 rounded-circle">
                                            <i class="bi bi-people-fill text-primary"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card metric-card border-0 shadow-sm h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small text-uppercase fw-semibold">Academic Programs</h6>
                                            <h3 class="mb-0 fw-bold">{{ $stats['subjects'] ?? 0 }}</h3>
                                            <div class="d-flex gap-3 mt-2">
                                                <p class="text-info mb-0 small">
                                                    <i class="bi bi-bookmark"></i> {{ $stats['subject_labels'] ?? 0 }}
                                                    Categories
                                                </p>
                                                <p class="text-success mb-0 small">
                                                    <i class="bi bi-book"></i> {{ $stats['subjects'] ?? 0 }} Subjects
                                                </p>
                                            </div>
                                        </div>
                                        <div class="metric-icon bg-success bg-opacity-10 rounded-circle">
                                            <i class="bi bi-book text-success"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card metric-card border-0 shadow-sm h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small text-uppercase fw-semibold">Active Sections</h6>
                                            <h3 class="mb-0 fw-bold">{{ $stats['sections'] ?? 0 }}</h3>
                                            <p class="text-success mb-0 mt-2 small">
                                                <i class="bi bi-check-circle"></i> Operational classes
                                            </p>
                                        </div>
                                        <div class="metric-icon bg-info bg-opacity-10 rounded-circle">
                                            <i class="bi bi-mortarboard-fill text-info"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card metric-card border-0 shadow-sm h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-muted mb-1 small text-uppercase fw-semibold">Scheduled Events</h6>
                                            <h3 class="mb-0 fw-bold">{{ $stats['events'] ?? 0 }}</h3>
                                            <p class="text-warning mb-0 mt-2 small">
                                                <i class="bi bi-calendar-event"></i> Upcoming activities
                                            </p>
                                        </div>
                                        <div class="metric-icon bg-warning bg-opacity-10 rounded-circle">
                                            <i class="bi bi-calendar-event text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <style>
        /* Welcome Banner Styles */
        .welcome-banner .card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 1rem;
        }

        .welcome-banner .display-6 {
            font-size: 1.75rem;
            line-height: 1.2;
        }

        /* Stat Card Styles */
        .stat-card {
            border-radius: 1rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        /* Quick Action Card Styles */
        .quick-action-card {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #ffffff;
            border-radius: 1rem;
            text-decoration: none;
            color: inherit;
            transition: all 0.2s ease-in-out;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .quick-action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            color: inherit;
        }

        .quick-action-card .icon-wrapper {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }

        .quick-action-card .content h6 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        /* Clock Display Styles */
        .clock-display {
            text-align: center;
            padding: 1rem;
            background: rgba(13, 110, 253, 0.05);
            border-radius: 1rem;
            display: inline-block;
            min-width: 200px;
        }

        .clock-display .time {
            font-size: 2rem;
            font-weight: 600;
            color: #0d6efd;
            line-height: 1;
            margin-bottom: 0.25rem;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }

        .clock-display .date {
            font-size: 0.875rem;
            color: #6c757d;
            font-weight: 500;
        }

        /* Chart Styles */
        .chart-container {
            margin: 1rem 0;
        }

        /* Quick Actions Fixed Size */
        .quick-actions-fixed {
            height: fit-content;
            max-height: 600px;
            position: sticky;
            top: 2rem;
        }

        .quick-actions-fixed .quick-action-card {
            width: 100%;
            margin-bottom: 0;
        }

        /* Dashboard Panel Heights */
        .dashboard-left-panel,
        .dashboard-right-panel {
            min-height: 500px;
        }

        /* Metric Cards */
        .metric-card {
            border-radius: 1rem;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .metric-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        /* Administrative Actions Panel */
        .admin-actions-panel {
            height: fit-content;
            max-height: 120px;
        }

        .admin-action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 0.5rem;
            background: #ffffff;
            border-radius: 0.5rem;
            text-decoration: none;
            color: inherit;
            transition: all 0.2s ease-in-out;
            border: 1px solid rgba(0, 0, 0, 0.05);
            height: 60px;
            width: 100%;
            text-align: center;
        }

        .admin-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1);
            color: inherit;
        }

        .action-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.25rem;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .action-label span {
            font-weight: 600;
            font-size: 0.75rem;
            line-height: 1.1;
            margin: 0;
        }
    </style>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Update clock
                function updateClock() {
                    const now = new Date();
                    const timeElement = document.getElementById('current-time');
                    const dateElement = document.getElementById('current-date');

                    timeElement.textContent = now.toLocaleTimeString();
                    dateElement.textContent = now.toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                }

                updateClock();
                setInterval(updateClock, 1000);

                // Student Status Chart
                const studentStatusData = @json($studentStatuses);
                const statusColors = {
                    'active': '#198754',
                    'inactive': '#ffc107',
                    'dropped': '#dc3545',
                    'graduated': '#0dcaf0',
                    'transferred': '#6c757d'
                };

                // Calculate total students and percentages
                const totalStudents = Object.values(studentStatusData).reduce((sum, count) => sum + count, 0);
                const percentageData = Object.values(studentStatusData).map(count =>
                    totalStudents > 0 ? ((count / totalStudents) * 100) : 0
                );
                const countsData = Object.values(studentStatusData);

                const ctx = document.getElementById('studentStatusChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: Object.keys(studentStatusData).map(status => status.charAt(0).toUpperCase() + status.slice(1)),
                            datasets: [{
                                label: 'Percentage of Students',
                                data: percentageData,
                                backgroundColor: Object.keys(studentStatusData).map(status => statusColors[status] || '#6c757d'),
                                borderWidth: 0,
                                borderRadius: 5
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                },
                                tooltip: {
                                    callbacks: {
                                        label: function(context) {
                                            const percentage = context.parsed.y.toFixed(1);
                                            const count = countsData[context.dataIndex];
                                            const total = totalStudents;
                                            return [
                                                `${percentage}% of students`,
                                                `${count} out of ${total} students`
                                            ];
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    ticks: {
                                        stepSize: 10,
                                        font: {
                                            size: 12
                                        },
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.1)'
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 12
                                        }
                                    },
                                    grid: {
                                        display: false
                                    }
                                }
                            },
                            onHover: (event, activeElements) => {
                                event.native.target.style.cursor = activeElements.length > 0 ? 'pointer' : 'default';
                            }
                        },
                        plugins: [{
                            id: 'dataLabels',
                            afterDatasetsDraw: function(chart) {
                                const ctx = chart.ctx;
                                chart.data.datasets.forEach((dataset, i) => {
                                    const meta = chart.getDatasetMeta(i);
                                    meta.data.forEach((bar, index) => {
                                        const data = dataset.data[index];

                                        if (data > 0) {
                                            ctx.fillStyle = '#374151';
                                            ctx.font = 'bold 11px Arial';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';

                                            // Display only percentage on top of bar
                                            const percentage = data.toFixed(1) + '%';
                                            ctx.fillText(percentage, bar.x, bar.y - 8);
                                        }
                                    });
                                });
                            }
                        }]
                    });
                }
            });
        </script>
    @endpush
@endsection