<!-- resources/views/dashboard.blade.php -->
@extends('layouts.teacherApp')

@section('content')
<div class="container-fluid py-4">
    <!-- Welcome Banner -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-4 d-flex flex-wrap align-items-center justify-content-between">
            <div>
                <h2 class="fw-bold text-primary mb-1">Welcome, {{ Auth::user()->name }}!</h2>
                <p class="text-muted mb-0">Here's your teaching overview for today.</p>
            </div>
            <div class="clock-display text-end">
                <div class="time" id="current-time">00:00:00</div>
                <div class="date" id="current-date">Loading...</div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row g-3 mb-4">
        <div class="col-md-3 col-6">
            <div class="card shadow-sm border-0 text-center">
                <div class="card-body">
                    <i class="bi bi-people-fill fs-2 text-primary mb-2"></i>
                    <h5 class="fw-bold mb-0">{{ $studentsCount ?? 0 }}</h5>
                    <div class="text-muted small">Students</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6">
            <div class="card shadow-sm border-0 text-center">
                <div class="card-body">
                    <i class="bi bi-journal-bookmark-fill fs-2 text-success mb-2"></i>
                    <h5 class="fw-bold mb-0">{{ $subjectsCount ?? 0 }}</h5>
                    <div class="text-muted small">Subjects</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6">
            <div class="card shadow-sm border-0 text-center">
                <div class="card-body">
                    <i class="bi bi-pencil-square fs-2 text-warning mb-2"></i>
                    <h5 class="fw-bold mb-0">{{ $pendingGrades ?? 0 }}</h5>
                    <div class="text-muted small">Pending Grades</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-6">
            <div class="card shadow-sm border-0 text-center">
                <div class="card-body">
                    <i class="bi bi-calendar-event fs-2 text-info mb-2"></i>
                    <h5 class="fw-bold mb-0">{{ $upcomingEvents ?? 0 }}</h5>
                    <div class="text-muted small">Upcoming Events</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- My Section -->
        <div class="col-lg-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0"><i class="bi bi-people me-2"></i>My Section</h5>
                </div>
                <div class="card-body">
                    @if(!empty($students) && count($students) > 0)
                        <ul class="list-group list-group-flush">
                            @foreach($students as $student)
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>
                                        <i class="bi bi-person-circle text-primary me-2"></i>
                                        {{ $student->user->last_name }}, {{ $student->user->first_name }}
                                    </span>
                                    <a href="{{ route('teachers.student.grade.show', $student->user_id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-journal-text me-1"></i> Grades
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-muted text-center py-3">No students found in your section.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- My Subjects -->
        <div class="col-lg-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0"><i class="bi bi-journal-bookmark me-2"></i>My Subjects</h5>
                </div>
                <div class="card-body">
                    @if(!empty($subjects) && count($subjects) > 0)
                        <ul class="list-group list-group-flush">
                            @foreach($subjects as $subject)
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>
                                        <i class="bi bi-book text-success me-2"></i>
                                        {{ $subject->name }}
                                    </span>
                                    <a href="{{ route('teachers.subject.show', $subject->id) }}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-people me-1"></i> View
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-muted text-center py-3">No subjects assigned.</div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Upcoming Events -->
        <div class="col-lg-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0"><i class="bi bi-calendar-event me-2"></i>Upcoming Events</h5>
                </div>
                <div class="card-body">
                    @if(!empty($events) && count($events) > 0)
                        <ul class="list-group list-group-flush">
                            @foreach($events as $event)
                                <li class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-medium">{{ $event->title }}</div>
                                            <div class="small text-muted">
                                                <i class="bi bi-calendar me-1"></i>{{ \Carbon\Carbon::parse($event->event_date)->format('F d, Y') }}
                                                @if($event->event_time)
                                                    <i class="bi bi-clock ms-2 me-1"></i>{{ \Carbon\Carbon::parse($event->event_time)->format('h:i A') }}
                                                @endif
                                            </div>
                                            @if($event->location)
                                                <div class="small text-muted">
                                                    <i class="bi bi-geo-alt me-1"></i>{{ $event->location }}
                                                </div>
                                            @endif
                                        </div>
                                        <form action="{{ route('teachers.event.mark-viewed', ['event' => $event->event_id]) }}" method="POST" class="d-inline mark-viewed-form" data-event-id="{{ $event->event_id }}">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline-primary mark-viewed-btn">
                                                <i class="bi bi-check2"></i> Mark as Viewed
                                            </button>
                                        </form>
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-muted text-center py-3">No upcoming events.</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-6">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0"><i class="bi bi-clock-history me-2"></i>Recent Activity</h5>
                </div>
                <div class="card-body">
                    @if(!empty($activities) && count($activities) > 0)
                        <ul class="timeline">
                            @foreach($activities as $activity)
                                <li>
                                    <span class="timeline-dot"></span>
                                    <span class="timeline-content">{{ $activity }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <div class="text-muted text-center py-3">No recent activity.</div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Styles for dashboard -->
<style>
.clock-display {
    text-align: right;
    padding: 0.75rem;
    background: rgba(13, 110, 253, 0.05);
    border-radius: 0.75rem;
    min-width: 180px;
    transition: all 0.3s ease;
}
.clock-display:hover {
    background: rgba(13, 110, 253, 0.1);
    transform: translateY(-2px);
}
.clock-display .time {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0d6efd;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
}
.clock-display .date {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08) !important;
}

/* List group items */
.list-group-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}
.list-group-item:hover {
    background-color: rgba(13, 110, 253, 0.03);
    border-left-color: #0d6efd;
}

/* Buttons */
.btn {
    transition: all 0.2s ease;
}
.btn:hover {
    transform: translateY(-1px);
}
.btn-outline-primary:hover {
    background-color: #0d6efd;
    color: white;
}

/* Timeline styling */
.timeline {
    list-style: none;
    padding-left: 0;
    position: relative;
}
.timeline li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}
.timeline li:hover {
    transform: translateX(5px);
}
.timeline-dot {
    position: absolute;
    left: 0;
    top: 0.4rem;
    width: 0.7rem;
    height: 0.7rem;
    background: #0d6efd;
    border-radius: 50%;
    display: inline-block;
    transition: all 0.3s ease;
}
.timeline li:hover .timeline-dot {
    transform: scale(1.2);
    box-shadow: 0 0 0 4px rgba(13, 110, 253, 0.1);
}
.timeline-content {
    font-size: 0.95rem;
}

/* Quick stats cards */
.card-body i {
    transition: all 0.3s ease;
}
.card:hover .card-body i {
    transform: scale(1.1);
}

/* Event items */
.event-item {
    transition: all 0.2s ease;
}
.event-item:hover {
    background-color: rgba(13, 110, 253, 0.03);
}

/* Form controls */
.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
    border-color: #0d6efd;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}
::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}
::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Mark as Viewed button states */
.mark-viewed-btn {
    position: relative;
    overflow: hidden;
}
.mark-viewed-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}
.mark-viewed-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin: -10px 0 0 -10px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: button-loading-spinner 0.6s linear infinite;
}
@keyframes button-loading-spinner {
    from {
        transform: rotate(0turn);
    }
    to {
        transform: rotate(1turn);
    }
}
</style>

<!-- Clock Script -->
<script>
function updateClock() {
    const now = new Date();
    const time = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const date = now.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    document.getElementById('current-time').textContent = time;
    document.getElementById('current-date').textContent = date;
}
updateClock();
setInterval(updateClock, 1000);

// Mark as Viewed functionality
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.mark-viewed-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const button = this.querySelector('.mark-viewed-btn');
            const eventId = this.dataset.eventId;
            
            // Add loading state
            button.classList.add('loading');
            button.innerHTML = ''; // Clear button content
            
            // Send AJAX request
            fetch(this.action, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the event item with animation
                    const eventItem = this.closest('.list-group-item');
                    eventItem.style.transition = 'all 0.3s ease';
                    eventItem.style.opacity = '0';
                    eventItem.style.transform = 'translateX(20px)';
                    
                    setTimeout(() => {
                        eventItem.remove();
                        
                        // Check if there are any events left
                        const remainingEvents = document.querySelectorAll('.list-group-item');
                        if (remainingEvents.length === 0) {
                            const eventsList = document.querySelector('.list-group');
                            eventsList.innerHTML = '<div class="text-muted text-center py-3">No upcoming events.</div>';
                        }
                        
                        // Update the events count
                        const eventsCount = document.querySelector('.upcoming-events-count');
                        if (eventsCount) {
                            const currentCount = parseInt(eventsCount.textContent);
                            eventsCount.textContent = Math.max(0, currentCount - 1);
                        }
                    }, 300);
                } else {
                    // Show error state
                    button.classList.remove('loading');
                    button.innerHTML = '<i class="bi bi-x"></i> Error';
                    button.classList.add('btn-danger');
                    
                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.classList.remove('btn-danger');
                        button.innerHTML = '<i class="bi bi-check2"></i> Mark as Viewed';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                button.classList.remove('loading');
                button.innerHTML = '<i class="bi bi-x"></i> Error';
                button.classList.add('btn-danger');
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    button.classList.remove('btn-danger');
                    button.innerHTML = '<i class="bi bi-check2"></i> Mark as Viewed';
                }, 2000);
            });
        });
    });
});
</script>
@endsection
